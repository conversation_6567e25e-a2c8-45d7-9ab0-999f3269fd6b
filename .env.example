SECRET_KEY=************************************************

# Security Headers Configuration
SECURITY_HEADERS_ENABLED=true
SECURITY_HSTS_ENABLED=true
SECURITY_HSTS_MAX_AGE=31536000
SECURITY_HSTS_INCLUDE_SUBDOMAINS=true
SECURITY_HSTS_PRELOAD=false
SECURITY_CSP_ENABLED=true
SECURITY_CSP_DEFAULT_SRC='self'
SECURITY_CSP_SCRIPT_SRC='self' 'unsafe-inline'
SECURITY_CSP_STYLE_SRC='self' 'unsafe-inline'
SECURITY_CSP_IMG_SRC='self' data: https:
SECURITY_CSP_FONT_SRC='self'
SECURITY_CSP_CONNECT_SRC='self'
SECURITY_CSP_FRAME_ANCESTORS='none'
SECURITY_X_FRAME_OPTIONS=DENY
SECURITY_X_CONTENT_TYPE_OPTIONS=true
SECURITY_X_XSS_PROTECTION=true
SECURITY_REFERRER_POLICY=strict-origin-when-cross-origin
SECURITY_PERMISSIONS_POLICY_ENABLED=true
SECURITY_PERMISSIONS_POLICY=geolocation=(), microphone=(), camera=(), payment=(), usb=(), magnetometer=(), gyroscope=(), speaker=()
# Server Header Control (mutually exclusive)
SECURITY_HIDE_SERVER_HEADER=true
# SECURITY_SERVER_HEADER_VALUE=Madcrow-API  # Only used when HIDE=false

# API Server Configuration
APP_NAME="Madcrow-API-Server"
APP_VERSION=0.0.1
APP_INSTRUCTION="This is the APP server for Madcrow."

# Pagination Configuration (Optional - uses defaults if not set)
# Global default settings for all endpoints
PAGINATION_DEFAULT_PAGE_SIZE=20
PAGINATION_MAX_PAGE_SIZE=100
PAGINATION_MIN_PAGE_SIZE=1

# Use case specific overrides (optional)
PAGINATION_LARGE_DATASET_PAGE_SIZE=50
PAGINATION_LARGE_DATASET_MAX_PAGE_SIZE=500
PAGINATION_SMALL_DATASET_PAGE_SIZE=10
PAGINATION_SMALL_DATASET_MAX_PAGE_SIZE=50
PAGINATION_MOBILE_PAGE_SIZE=10
PAGINATION_MOBILE_MAX_PAGE_SIZE=25

#Development Environment Configuration
DEBUG=true
DEPLOY_ENV=DEVELOPMENT

BACKEND_APP_BIND_ADDRESS=0.0.0.0
BACKEND_APP_PORT=5001
GUNICORN_TIMEOUT=360

#Log Config
# Supported values are `DEBUG`, `INFO`, `WARNING`, `ERROR`, `CRITICAL`
LOG_LEVEL=INFO
# Log file path
LOG_FOLDER=logs
# Log file max size, the unit is MB
LOG_FILE_MAX_SIZE=20
# Log file max backup count
LOG_FILE_BACKUP_COUNT=5
# Log dateformat
LOG_DATEFORMAT="%Y-%m-%d %H:%M:%S"
# Log Timezone
LOG_TZ=UTC
# Log format
LOG_FORMAT="%(asctime)s,%(msecs)03d %(levelname)-2s [%(filename)s:%(lineno)d] [%(req_id)s] %(message)s"

API_COMPRESSION_ENABLED=false

# CORS Configuration
WEB_API_CORS_ALLOW_ORIGINS=*

# HTTP Request Configuration
HTTP_REQUEST_MAX_CONNECT_TIMEOUT=300
HTTP_REQUEST_MAX_READ_TIMEOUT=600
HTTP_REQUEST_MAX_WRITE_TIMEOUT=600
HTTP_REQUEST_NODE_MAX_BINARY_SIZE=10485760
HTTP_REQUEST_NODE_MAX_TEXT_SIZE=1048576

SSRF_PROXY_ALL_URL=
SSRF_PROXY_HTTP_URL=
SSRF_PROXY_HTTPS_URL=
SSRF_DEFAULT_MAX_RETRIES=3
SSRF_DEFAULT_TIME_OUT=5
SSRF_DEFAULT_CONNECT_TIME_OUT=5
SSRF_DEFAULT_READ_TIME_OUT=5
SSRF_DEFAULT_WRITE_TIME_OUT=5

RESPECT_XFORWARD_HEADERS_ENABLED=false

DB_USERNAME=postgres
DB_PASSWORD=123456
DB_HOST=localhost
DB_PORT=5432
DB_DATABASE=madcrow
DB_CHARSET=
DB_EXTRAS=

SQLALCHEMY_DATABASE_URI_SCHEME=postgresql+psycopg
SQLALCHEMY_ECHO=false
SQLALCHEMY_POOL_SIZE=30
SQLALCHEMY_MAX_OVERFLOW=10
SQLALCHEMY_POOL_RECYCLE=3600
SQLALCHEMY_POOL_PRE_PING=false
DB_CONNECTION_TEST_ON_STARTUP=false

# redis configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_USERNAME=
REDIS_PASSWORD=aiagent1234
REDIS_USE_SSL=false
REDIS_DB=0

# redis Sentinel configuration.
REDIS_USE_SENTINEL=false
REDIS_SENTINELS=
REDIS_SENTINEL_SERVICE_NAME=
REDIS_SENTINEL_USERNAME=
REDIS_SENTINEL_PASSWORD=
REDIS_SENTINEL_SOCKET_TIMEOUT=0.1

# redis Cluster configuration.
REDIS_USE_CLUSTERS=false
REDIS_CLUSTERS=
REDIS_CLUSTERS_PASSWORD=

# Rate Limiting Configuration
RATE_LIMIT_LOGIN_ENABLED=true
# Maximum failed login attempts before rate limiting (0 = emergency lockdown mode)
RATE_LIMIT_LOGIN_MAX_ATTEMPTS=5
RATE_LIMIT_LOGIN_TIME_WINDOW=900
