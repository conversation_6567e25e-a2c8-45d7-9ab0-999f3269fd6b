# Simple Reusable Pagination System

Clean, class-based pagination system for FastAPI Madcrow project .

## 📋 Overview

The pagination system provides:

- **Simple class-based service** similar to health service
- **Environment-configurable settings** via .env variables
- **FastAPI dependencies** for easy integration
- **Generic response models** for consistent API responses
- **Multiple configuration types** for different use cases

## 🏗️ Architecture

### Core Components

```
src/utils/pagination.py          # Main pagination service and utilities
src/dependencies/pagination.py   # FastAPI dependencies
src/models/pagination.py         # Response models
```

### Key Classes

- **`PaginationService`**: Main service class (similar to HealthService) - in `src.utils.pagination`
- **`PaginationConfig`**: Configuration with environment support - in `src.models.pagination`
- **`PaginationMeta`**: Metadata about pagination state - in `src.models.pagination`
- **`PaginatedResponse`**: Generic response model - in `src.models.pagination`

**Note**: All Pydantic models are now centralized in `src.models.pagination` to eliminate duplication.

## 🚀 Quick Start

### 1. Basic Usage with Dependencies (Recommended)

```python
from fastapi import APIRouter
from src.dependencies.pagination import StandardPaginationDep, PaginationServiceDep
from src.models.pagination import PaginatedResponse

@router.get("/users", response_model=PaginatedResponse[UserResponse])
async def list_users(
    pagination: StandardPaginationDep,
    pagination_service: PaginationServiceDep,
):
    page, page_size = pagination

    query = select(User).order_by(User.created_at.desc())
    users, pagination_meta = pagination_service.paginate_query(
        query, page, page_size, config_type="standard"
    )

    user_responses = [UserResponse.model_validate(user) for user in users]
    return PaginatedResponse.create(user_responses, page, page_size, pagination_meta.total_items)
```

### 2. Using Utility Function (Simple)

```python
from src.utils.pagination import paginate_sqlmodel_query

@router.get("/items", response_model=PaginatedResponse[ItemResponse])
async def list_items(
    pagination: StandardPaginationDep,
    db_session: Session = Depends(get_session),
):
    page, page_size = pagination
    query = select(Item).order_by(Item.created_at.desc())

    items, pagination_meta = paginate_sqlmodel_query(
        db_session, query, page, page_size, "standard"
    )

    item_responses = [ItemResponse.model_validate(item) for item in items]
    return PaginatedResponse.create(item_responses, page, page_size, pagination_meta.total_items)
```

## ⚙️ Configuration Options

### Predefined Configurations

```python
from src.utils.pagination import PaginationConfigs

# Standard pagination (20 items, max 100)
PaginationConfigs.STANDARD

# Large datasets (50 items, max 500)
PaginationConfigs.LARGE_DATASET

# Small datasets (10 items, max 50)
PaginationConfigs.SMALL_DATASET

# Mobile optimized (10 items, max 25)
PaginationConfigs.MOBILE
```

### Custom Configuration

```python
from src.utils.pagination import PaginationConfig

custom_config = PaginationConfig(
    default_page_size=30,
    max_page_size=200,
    min_page_size=5,
    default_page=1,
)
```

## 🔧 Available Dependencies

### Pagination Parameter Dependencies

```python
from src.dependencies.pagination import (
    StandardPaginationDep,           # 20 items, max 100
    LargeDatasetPaginationDep,       # 50 items, max 500
    SmallDatasetPaginationDep,       # 10 items, max 50
    MobilePaginationDep,             # 10 items, max 25
)

@router.get("/items")
async def list_items(pagination: StandardPaginationDep):
    page, page_size = pagination
    # Use page and page_size...
```

### Pagination Service Dependencies

```python
from src.dependencies.pagination import StandardPaginationServiceDep

@router.get("/items")
async def list_items(
    pagination_service: StandardPaginationServiceDep,
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
):
    query = select(Item)
    items, pagination_meta = pagination_service.paginate_query(query, page, page_size)
    # Use items and pagination_meta...
```

## 📊 Response Models

### Modern Response Format

```python
from src.models.pagination import PaginatedResponse

# Response structure:
{
    "items": [...],
    "pagination": {
        "page": 1,
        "page_size": 20,
        "total_items": 100,
        "total_pages": 5,
        "has_next": true,
        "has_previous": false,
        "next_page": 2,
        "previous_page": null
    }
}
```

### Legacy Compatible Response

```python
from src.models.pagination import PaginatedResponseWithLegacy

# Response structure (includes legacy fields):
{
    "items": [...],
    "pagination": { ... },
    "total": 100,        # Legacy field
    "page": 1,           # Legacy field
    "page_size": 20,     # Legacy field
    "total_pages": 5     # Legacy field
}
```

## 🎯 Usage Patterns

### 1. Simple Pagination

```python
@router.get("/plans", response_model=PaginatedResponse[PlanResponse])
async def list_plans(pagination: StandardPaginationDep, db_session: Session = Depends(get_session)):
    page, page_size = pagination
    query = select(Plan).order_by(Plan.created_at.desc())

    plans, pagination_meta = paginate_sqlmodel_query(db_session, query, page, page_size)
    plan_responses = [PlanResponse.model_validate(plan) for plan in plans]

    return PaginatedResponse.create(plan_responses, page, page_size, pagination_meta.total_items)
```

### 2. Pagination with Filtering

```python
@router.get("/users", response_model=PaginatedResponse[UserResponse])
async def list_users(
    pagination: StandardPaginationDep,
    active_only: bool = Query(True),
    search: str = Query(None),
    db_session: Session = Depends(get_session),
):
    page, page_size = pagination
    query = select(User)

    if active_only:
        query = query.where(User.is_active == True)

    if search:
        query = query.where(User.name.ilike(f"%{search}%"))

    query = query.order_by(User.created_at.desc())

    users, pagination_meta = paginate_sqlmodel_query(db_session, query, page, page_size)
    user_responses = [UserResponse.model_validate(user) for user in users]

    return PaginatedResponse.create(user_responses, page, page_size, pagination_meta.total_items)
```

### 3. Custom Pagination Configuration

```python
@router.get("/reports", response_model=PaginatedResponse[ReportResponse])
async def list_reports(
    db_session: Session = Depends(get_session),
    page: int = Query(1, ge=1),
    page_size: int = Query(100, ge=1, le=1000),  # Large page sizes for reports
):
    # Custom config for reports
    config = PaginationConfig(default_page_size=100, max_page_size=1000)
    pagination_service = PaginationService(db_session, config)

    query = select(Report).order_by(Report.created_at.desc())
    reports, pagination_meta = pagination_service.paginate_query(query, page, page_size)

    report_responses = [ReportResponse.model_validate(report) for report in reports]
    return PaginatedResponse.create(report_responses, page, page_size, pagination_meta.total_items)
```

### 4. Reusable Pagination Function

```python
def paginate_users(
    db_session: Session,
    page: int,
    page_size: int,
    filters: dict = None,
) -> tuple[list[User], int]:
    """Reusable user pagination with common filters."""
    query = select(User)

    if filters:
        if filters.get("active_only"):
            query = query.where(User.is_active == True)
        if filters.get("search"):
            query = query.where(User.name.ilike(f"%{filters['search']}%"))

    query = query.order_by(User.created_at.desc())

    users, pagination_meta = paginate_sqlmodel_query(db_session, query, page, page_size)
    return users, pagination_meta.total_items

@router.get("/users", response_model=PaginatedResponse[UserResponse])
async def list_users(
    pagination: StandardPaginationDep,
    active_only: bool = Query(True),
    search: str = Query(None),
    db_session: Session = Depends(get_session),
):
    page, page_size = pagination
    filters = {"active_only": active_only, "search": search}

    users, total_count = paginate_users(db_session, page, page_size, filters)
    user_responses = [UserResponse.model_validate(user) for user in users]

    return PaginatedResponse.create(user_responses, page, page_size, total_count)
```

## 🔍 Advanced Features

### Performance Optimization

```python
# Use separate count query for better performance on large datasets
def list_items_optimized(db_session: Session, page: int, page_size: int):
    # Main query
    query = select(Item).order_by(Item.created_at.desc())

    # Optimized count query (only count IDs)
    count_query = select(Item.id)

    pagination_service = PaginationService(db_session)
    items, pagination_meta = pagination_service.paginate_query(
        query, page, page_size, count_query=count_query
    )

    return items, pagination_meta
```

### Error Handling

```python
from src.utils.pagination import PaginationService

def safe_paginate(db_session: Session, query, page: int, page_size: int):
    try:
        pagination_service = PaginationService(db_session)
        return pagination_service.paginate_query(query, page, page_size)
    except ValueError as e:
        # Handle validation errors
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        # Handle other errors
        raise HTTPException(status_code=500, detail="Pagination failed")
```

## 🧪 Testing

The pagination system includes comprehensive tests:

```bash
# Run pagination tests
uv run pytest tests/unit/test_pagination.py -v

# Test pagination in plan endpoints
uv run pytest tests/api/test_plan_endpoints.py::TestListPlansEndpoint::test_list_plans_pagination -v
```

## 📈 Migration from Existing Pagination

### Before (Manual Pagination)

```python
@router.get("/plans")
async def list_plans(
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
):
    offset = (page - 1) * page_size
    query = select(Plan).offset(offset).limit(page_size)
    plans = db_session.exec(query).all()

    # Manual count query
    total = len(db_session.exec(select(Plan.id)).all())
    total_pages = (total + page_size - 1) // page_size

    return {
        "plans": plans,
        "total": total,
        "page": page,
        "page_size": page_size,
        "total_pages": total_pages,
    }
```

### After (Reusable Pagination)

```python
@router.get("/plans", response_model=PaginatedResponse[PlanResponse])
async def list_plans(pagination: StandardPaginationDep, db_session: Session = Depends(get_session)):
    page, page_size = pagination
    query = select(Plan).order_by(Plan.created_at.desc())

    plans, pagination_meta = paginate_sqlmodel_query(db_session, query, page, page_size)
    plan_responses = [PlanResponse.model_validate(plan) for plan in plans]

    return PaginatedResponse.create(plan_responses, page, page_size, pagination_meta.total_items)
```

## ✅ Benefits

- **Consistency**: Same pagination behavior across all endpoints
- **Configurability**: Easy to adjust pagination settings per use case
- **Type Safety**: Full type hints and Pydantic validation
- **Performance**: Optimized database queries with separate count queries
- **Testing**: Comprehensive test coverage
- **Documentation**: Auto-generated OpenAPI documentation
- **Backward Compatibility**: Legacy response format support

## 🔧 Configuration Reference

| Parameter           | Default | Description                      |
| ------------------- | ------- | -------------------------------- |
| `default_page_size` | 20      | Default number of items per page |
| `max_page_size`     | 100     | Maximum allowed page size        |
| `min_page_size`     | 1       | Minimum allowed page size        |
| `default_page`      | 1       | Default page number              |

## 📚 Examples

See `src/examples/pagination_usage.py` for comprehensive usage examples including:

- Standard pagination patterns
- Custom configurations
- Mobile optimization
- Large dataset handling
- Search and filtering integration
- Reusable pagination functions
