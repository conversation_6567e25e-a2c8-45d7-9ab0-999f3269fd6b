# Admin Profile Management

## Overview

The admin profile management system provides comprehensive user administration capabilities through enhanced profile update endpoints. Administrators can manage profiles for all users in the system, including other administrators, with a unified interface for profile updates with proper permission controls.

## Endpoint

### Profile Update (Enhanced)

```
PATCH /api/v1/profile/update[?user_id={uuid}]
```

## Authentication & Permissions

- **Regular Users**: Can only update their own profile with standard fields
- **Admin Users**: Can update any user's profile (including other admins) with all fields including admin-only fields

## Request Parameters

| Parameter | Type | Required | Description                                                           |
| --------- | ---- | -------- | --------------------------------------------------------------------- |
| `user_id` | UUID | No       | User ID to update (admin only). If not provided, updates current user |

## Request Body Fields

### Standard Fields (All Users)

- `name` (string, optional): User display name
- `timezone` (string, optional): User timezone
- `avatar` (string, optional): User avatar URL

### Admin-Only Fields (Admins Only)

- `email` (string, optional): User email address
- `status` (AccountStatus, optional): Account status (active, banned, etc.)
- `is_admin` (boolean, optional): Admin privileges

## Usage Examples

### 1. Regular User Updates Own Profile

```bash
curl -X PATCH "/api/v1/profile/update" \
  -H "Authorization: Bearer {user_token}" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Updated User Name",
    "timezone": "America/New_York",
    "avatar": "https://example.com/avatar.jpg"
  }'
```

### 2. Admin Updates Regular User Profile

```bash
curl -X PATCH "/api/v1/profile/update?user_id={user_id}" \
  -H "Authorization: Bearer {admin_token}" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Updated by Admin",
    "email": "<EMAIL>",
    "status": "active",
    "is_admin": false
  }'
```

### 3. Admin Updates Another Admin Profile

```bash
curl -X PATCH "/api/v1/profile/update?user_id={admin_id}" \
  -H "Authorization: Bearer {admin_token}" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Updated Admin Name",
    "email": "<EMAIL>",
    "status": "active",
    "is_admin": true
  }'
```

### 4. Admin Updates Own Profile

```bash
curl -X PATCH "/api/v1/profile/update" \
  -H "Authorization: Bearer {admin_token}" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "My Updated Name",
    "timezone": "Europe/London"
  }'
```

### 5. Admin Promotes User to Admin

```bash
curl -X PATCH "/api/v1/profile/update?user_id={user_id}" \
  -H "Authorization: Bearer {admin_token}" \
  -H "Content-Type: application/json" \
  -d '{
    "is_admin": true
  }'
```

### 6. Admin Demotes Admin to User

```bash
curl -X PATCH "/api/v1/profile/update?user_id={admin_id}" \
  -H "Authorization: Bearer {admin_token}" \
  -H "Content-Type: application/json" \
  -d '{
    "is_admin": false
  }'
```

## Response Format

```json
{
  "success": true,
  "message": "Profile updated successfully by admin",
  "user": {
    "id": "uuid",
    "name": "Updated Name",
    "email": "<EMAIL>",
    "status": "active",
    "timezone": "UTC",
    "avatar": "https://example.com/avatar.jpg",
    "is_admin": true,
    "last_login_at": "2024-01-01T00:00:00Z",
    "initialized_at": "2024-01-01T00:00:00Z",
    "created_at": "2024-01-01T00:00:00Z"
  },
  "updated_at": "2024-01-01T00:00:00Z",
  "updated_by_admin": true,
  "admin_fields_updated": ["email", "status", "is_admin"]
}
```

## Response Fields

- `success`: Boolean indicating operation success
- `message`: Human-readable success message
- `user`: Updated user profile object
- `updated_at`: Timestamp of the update
- `updated_by_admin`: Boolean indicating if update was performed by admin on another user
- `admin_fields_updated`: Array of admin-only fields that were modified

## Permission Matrix

| User Type    | Own Profile             | Other User    | Other Admin   | Admin Fields |
| ------------ | ----------------------- | ------------- | ------------- | ------------ |
| Regular User | ✅ Standard fields only | ❌            | ❌            | ❌           |
| Admin User   | ✅ All fields           | ✅ All fields | ✅ All fields | ✅           |

### Field-Level Permissions

| Field      | Regular User (Own) | Admin User (Any) |
| ---------- | ------------------ | ---------------- |
| `name`     | ✅                 | ✅               |
| `timezone` | ✅                 | ✅               |
| `avatar`   | ✅                 | ✅               |
| `email`    | ❌                 | ✅               |
| `status`   | ❌                 | ✅               |
| `is_admin` | ❌                 | ✅               |

## Error Responses

### 403 Forbidden - Non-admin trying to update other user

```json
{
  "detail": "Admin privileges required to update other users"
}
```

### 403 Forbidden - Non-admin trying to update admin fields

```json
{
  "detail": "Admin privileges required to update fields: email, status, is_admin"
}
```

### 404 Not Found - User doesn't exist

```json
{
  "detail": "User not found"
}
```

### 400 Bad Request - Email already in use

```json
{
  "detail": "Email address is already in use"
}
```

## Security Features

- **Permission Validation**: Automatic checking of admin privileges
- **Field-Level Security**: Admin-only fields protected from regular users
- **Audit Trail**: Tracks who performed updates and which fields were modified
- **Email Uniqueness**: Prevents duplicate email addresses
- **Input Validation**: All fields validated using modern FieldValidators

## Best Practices

1. **Use specific updates**: Only include fields you want to change
2. **Check permissions**: Ensure the requesting user has appropriate privileges
3. **Validate emails**: Ensure email addresses are unique before updating
4. **Monitor admin actions**: Track admin field updates for security auditing
5. **Handle errors gracefully**: Implement proper error handling for all scenarios
