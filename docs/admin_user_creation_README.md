# Admin User Creation - Complete Guide

## Overview

The Admin User Creation feature allows administrators to create complete user setups including user accounts, workspaces, subscriptions, and payment records through a single API endpoint. This document provides everything you need to understand, implement, and use this feature.

## 🎯 **Admin Workflow**

The complete admin user creation flow:

1. **Admin Login** → Dashboard access
2. **User Creation** → Auto-create workspace (`user_name_workspace`)
3. **Workspace Owner** → User becomes owner in `workspace_members`
4. **Plan Selection** → Admin selects from available plans
5. **Trial Choice** → Admin explicitly chooses trial (yes/no)
6. **Payment Entry** → Auto-calculated (0 for trial, plan price for paid)
7. **Auto Reference** → Auto-generate payment reference (`trial_subscriptionid`, `monthly_subscriptionid`, `yearly_subscriptionid`)
8. **Audit Logging** → All actions logged with admin as actor

## 🚀 **Key Features**

- **Clean API Design**: Explicit trial choice, auto-generated values, clear validation
- **Auto-Generated Workspace Names**: `user_name_workspace` format
- **Auto-Calculated Payment Amounts**: 0 for trials, plan price for paid subscriptions
- **Auto-Generated Payment References**: Unique references prevent database conflicts
- **Comprehensive Validation**: Uses existing validation system consistently
- **Complete Audit Trail**: All actions logged with admin as actor
- **Database Transaction Safety**: All-or-nothing operations with rollback support

### **Auto-Generation Logic**

```javascript
// Workspace Names
"John Doe" → "john_doe_workspace"
"Jane Smith-Wilson" → "jane_smith_wilson_workspace"

// Payment References
Trial: "trial_12345678"
Monthly: "monthly_12345678"
Yearly: "yearly_12345678"

// Payment Amounts
Trial: 0.0 (always)
Monthly: plan.price_per_month
Yearly: plan.price_per_year
```

## 📋 **API Reference**

### **Endpoint**

```
POST /api/v1/admin/users/create
```

### **Authentication**

- **Required**: Admin privileges
- **Header**: `Authorization: Bearer {admin_token}`

### **Request Body**

| Field             | Type    | Required | Description                                            |
| ----------------- | ------- | -------- | ------------------------------------------------------ |
| `name`            | string  | ✅       | User's full name (2-255 characters)                    |
| `email`           | string  | ✅       | User's email address (must be unique)                  |
| `workspace_name`  | string  | ❌       | Custom workspace name (auto-generates if not provided) |
| `plan_id`         | UUID    | ✅       | ID of the subscription plan (must exist and be active) |
| `is_trial`        | boolean | ✅       | Whether this should be a trial subscription            |
| `billing_cycle`   | string  | ❌       | Required for paid subscriptions: "monthly" or "yearly" |
| `payment_remarks` | string  | ❌       | Optional payment notes for admin reference             |

### **Validation Rules**

#### **Business Logic**

- **Trial Subscriptions**: `is_trial: true`, no `billing_cycle` required
- **Paid Subscriptions**: `is_trial: false`, `billing_cycle` required ("monthly" or "yearly")
- **Plan Requirements**: Plan must exist, be active, and support trial if requested

#### **Field Validation**

- **Email**: Valid format, unique across all users
- **Name**: 2-255 characters, no XSS characters
- **Workspace Name**: 2-255 characters if provided
- **Plan ID**: Must be valid UUID of existing active plan

## 📝 **Usage Examples**

### **1. Trial Subscription (Auto-generated workspace)**

```json
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "plan_id": "123e4567-e89b-12d3-a456-************",
  "is_trial": true,
  "payment_remarks": "Trial subscription for new user"
}
```

**Result**: Creates user with workspace `john_doe_workspace`, $0 payment, `trial_12345678` reference

### **2. Paid Monthly Subscription (Custom workspace)**

```json
{
  "name": "Jane Smith",
  "email": "<EMAIL>",
  "workspace_name": "Jane's Company",
  "plan_id": "123e4567-e89b-12d3-a456-************",
  "is_trial": false,
  "billing_cycle": "monthly",
  "payment_remarks": "Monthly subscription for client"
}
```

**Result**: Creates user with workspace `Jane's Company`, plan monthly price, `monthly_12345678` reference

### **3. Paid Yearly Subscription (Auto-generated workspace)**

```json
{
  "name": "Bob Wilson",
  "email": "<EMAIL>",
  "plan_id": "123e4567-e89b-12d3-a456-************",
  "is_trial": false,
  "billing_cycle": "yearly",
  "payment_remarks": "Yearly subscription with discount"
}
```

**Result**: Creates user with workspace `bob_wilson_workspace`, plan yearly price, `yearly_12345678` reference

### **Response Format**

```json
{
  "success": true,
  "message": "Successfully <NAME_EMAIL> with workspace and subscription",
  "user_id": "456e7890-e89b-12d3-a456-426614174001",
  "workspace_id": "789e0123-e89b-12d3-a456-426614174002",
  "subscription_id": "012e3456-e89b-12d3-a456-426614174003",
  "payment_id": "345e6789-e89b-12d3-a456-426614174004",
  "user_email": "<EMAIL>",
  "user_name": "John Doe",
  "user_status": "pending",
  "workspace_name": "john_doe_workspace",
  "workspace_role": "owner",
  "plan_name": "Professional Plan",
  "billing_cycle": "trial",
  "is_trial": true,
  "subscription_start_date": "2024-01-01T00:00:00Z",
  "subscription_end_date": "2024-01-15T00:00:00Z",
  "amount_paid": "0.0",
  "payment_reference": "trial_12345678",
  "created_by_admin": "678e9012-e89b-12d3-a456-************",
  "created_at": "2024-01-01T00:00:00Z"
}
```

## 🔧 **Implementation Details**

### **Database Operations**

The workflow creates records in the following order:

1. **User Account** (`accounts` table) - Status: PENDING, no password
2. **Workspace** (`workspaces` table) - Auto-generated or custom name
3. **Workspace Membership** (`workspace_members` table) - User as OWNER
4. **Subscription** (`subscriptions` table) - Based on plan and billing cycle
5. **Payment Record** (`payments` table) - Auto-calculated amount and reference
6. **Audit Logs** (`audit_logs` table) - All actions with admin as actor

### **Auto-Generation Logic**

#### **Workspace Names**

```python
def generate_workspace_name(user_name):
    clean_name = user_name.lower().replace(" ", "_").replace("-", "_")
    clean_name = "".join(c for c in clean_name if c.isalnum() or c == "_")
    return f"{clean_name}_workspace"
```

#### **Payment References**

```python
def generate_payment_reference(subscription):
    if subscription.is_trial:
        return f"trial_{str(subscription.id)[:8]}"
    elif subscription.billing_cycle == "monthly":
        return f"monthly_{str(subscription.id)[:8]}"
    elif subscription.billing_cycle == "yearly":
        return f"yearly_{str(subscription.id)[:8]}"
```

#### **Payment Amounts**

```python
def calculate_payment_amount(plan, subscription):
    if subscription.is_trial:
        return 0.0
    elif subscription.billing_cycle == "monthly":
        return float(plan.price_per_month or 0.0)
    elif subscription.billing_cycle == "yearly":
        return float(plan.price_per_year or 0.0)
```

## 🔒 **Security & Validation**

### **Authentication & Authorization**

- **Admin Only**: Endpoint requires admin privileges
- **JWT Authentication**: Bearer token validation
- **Permission Checks**: Automatic admin role verification

### **Input Validation**

- **Email Format**: RFC compliant email validation
- **Email Uniqueness**: Database constraint enforcement
- **Name Validation**: Length limits and XSS protection
- **Field Limits**: All text fields have appropriate length limits
- **Business Logic**: Trial vs paid subscription validation

### **Error Handling**

- **Comprehensive Coverage**: All edge cases handled
- **User-Friendly Messages**: Clear, actionable error descriptions
- **Database Safety**: Transaction rollback on any failure
- **Audit Trail**: All actions (including failures) logged

### **Database Integrity**

- **Unique Constraints**: Email uniqueness enforced
- **Payment References**: Auto-generated to prevent duplicates
- **Transaction Safety**: All-or-nothing operations
- **Foreign Key Integrity**: Proper relationships maintained
- **Rollback Support**: Automatic cleanup on failures

## 🚀 **Production Deployment**

### **Curl Commands**

#### **Trial Subscription**

```bash
curl -X POST \
  "http://localhost:5001/api/v1/admin/users/create" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "John Doe",
    "email": "<EMAIL>",
    "plan_id": "YOUR_PLAN_ID",
    "is_trial": true,
    "payment_remarks": "Trial subscription"
  }'
```

#### **Paid Monthly Subscription**

```bash
curl -X POST \
  "http://localhost:5001/api/v1/admin/users/create" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Jane Smith",
    "email": "<EMAIL>",
    "workspace_name": "Custom Workspace",
    "plan_id": "YOUR_PLAN_ID",
    "is_trial": false,
    "billing_cycle": "monthly",
    "payment_remarks": "Monthly subscription"
  }'
```

#### **Paid Yearly Subscription**

```bash
curl -X POST \
  "http://localhost:5001/api/v1/admin/users/create" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Bob Wilson",
    "email": "<EMAIL>",
    "plan_id": "YOUR_PLAN_ID",
    "is_trial": false,
    "billing_cycle": "yearly",
    "payment_remarks": "Yearly subscription"
  }'
```

## 📊 **Error Responses**

### **Common Error Codes**

#### **400 Bad Request**

```json
{
  "detail": "Plan with ID 123e4567-e89b-12d3-a456-************ not found"
}
```

#### **422 Validation Error**

```json
{
  "detail": [
    {
      "type": "value_error",
      "loc": ["body", "email"],
      "msg": "Invalid email format",
      "input": "invalid-email"
    }
  ]
}
```

#### **409 Conflict**

```json
{
  "detail": "User <NAME_EMAIL> already exists"
}
```

## 🧪 **Testing**

### **Test Coverage: 12/12 PASSING**

#### **Workflow Tests**

- ✅ Trial subscription creation
- ✅ Paid monthly subscription creation
- ✅ Paid yearly subscription creation
- ✅ Auto-generated workspace names
- ✅ Custom workspace names

#### **Validation Tests**

- ✅ Duplicate email handling
- ✅ Invalid plan handling
- ✅ Permission validation (admin-only)
- ✅ Email format validation
- ✅ Name validation
- ✅ Billing cycle validation
- ✅ Trial vs paid logic validation

#### **Running Tests**

```bash
# Run all admin user creation tests
uv run python -m pytest tests/api/test_admin_user_creation.py -v

# Run specific test
uv run python -m pytest tests/api/test_admin_user_creation.py::TestAdminUserCreationWorkflow::test_admin_create_user_trial_subscription_success -v
```

## ✅ **Production Ready**

### **Checklist**

- ✅ **Complete Workflow**: User, workspace, subscription, payment creation
- ✅ **Auto-Generation**: Workspace names, payment amounts, payment references
- ✅ **Clean API**: Explicit trial choice, no confusing logic
- ✅ **Comprehensive Validation**: Email, name, business logic validation
- ✅ **Security**: Admin-only access, input sanitization, XSS protection
- ✅ **Error Handling**: All edge cases covered with clear messages
- ✅ **Database Safety**: Transaction integrity and rollback support
- ✅ **Audit Trail**: Complete logging for accountability
- ✅ **Test Coverage**: 12/12 tests passing
- ✅ **Documentation**: Complete usage guide and examples

### **Ready for Production**

The admin user creation workflow is **PRODUCTION READY** and implements your exact specifications:

1. **Clean, intuitive admin flow**
2. **Auto-generated workspace names** (`user_name_workspace`)
3. **Auto-calculated payment amounts** (0 for trials, plan price for paid)
4. **Auto-generated unique payment references** (`trial_`, `monthly_`, `yearly_` + ID)
5. **Explicit trial vs paid logic** (`is_trial: true/false`)
6. **Comprehensive validation and error handling**
7. **Complete audit logging with admin as actor**
8. **Database transaction safety and rollback support**

**The system provides a smooth, intuitive admin experience exactly as specified and is ready for immediate production deployment.**

## 🧪 **Test Cases**

### **Test Case 1: Trial Subscription**

**Description:** Create a new user with trial subscription and auto-generated workspace

**Request:**

```bash
curl -X 'POST' \
  'http://localhost:5001/api/v1/admin/users/create' \
  -H 'Authorization: Bearer {admin_token}' \
  -H 'Content-Type: application/json' \
  -d '{
    "name": "John Doe",
    "email": "<EMAIL>",
    "plan_id": "8437438a-55be-408a-8c2d-f950145ec5ad",
    "is_trial": true,
    "payment_remarks": "Trial subscription for new user"
  }'
```

**Expected Response:**

```json
{
  "success": true,
  "message": "Successfully <NAME_EMAIL> with workspace and subscription",
  "user_id": "456e7890-e89b-12d3-a456-426614174001",
  "workspace_id": "789e0123-e89b-12d3-a456-426614174002",
  "subscription_id": "012e3456-e89b-12d3-a456-426614174003",
  "payment_id": "345e6789-e89b-12d3-a456-426614174004",
  "user_email": "<EMAIL>",
  "user_name": "John Doe",
  "user_status": "pending",
  "workspace_name": "john_doe_workspace",
  "workspace_role": "owner",
  "plan_name": "Professional Plan",
  "billing_cycle": "trial",
  "is_trial": true,
  "subscription_start_date": "2024-01-01T00:00:00Z",
  "subscription_end_date": "2024-01-15T00:00:00Z",
  "amount_paid": "0.0",
  "payment_reference": "trial_12345678",
  "created_by_admin": "678e9012-e89b-12d3-a456-************",
  "created_at": "2024-01-01T00:00:00Z"
}
```

**What Happens:**

- ✅ Creates user with PENDING status (no password)
- ✅ Auto-generates workspace: `john_doe_workspace`
- ✅ Creates trial subscription using plan's trial duration
- ✅ Sets payment amount to $0.00
- ✅ Generates unique payment reference: `trial_{subscription_id}`
- ✅ Logs all actions in audit log

### **Test Case 2: Monthly Paid Subscription**

**Description:** Create a new user with monthly paid subscription and custom workspace name

**Request:**

```bash
curl -X 'POST' \
  'http://localhost:5001/api/v1/admin/users/create' \
  -H 'Authorization: Bearer {admin_token}' \
  -H 'Content-Type: application/json' \
  -d '{
    "name": "Jane Smith",
    "email": "<EMAIL>",
    "workspace_name": "Jane'\''s Company",
    "plan_id": "8437438a-55be-408a-8c2d-f950145ec5ad",
    "is_trial": false,
    "billing_cycle": "monthly",
    "payment_remarks": "Monthly subscription for client"
  }'
```

**What Happens:**

- ✅ Creates user with PENDING status
- ✅ Uses custom workspace name: `Jane's Company`
- ✅ Creates 30-day monthly subscription
- ✅ Sets payment amount to plan's monthly price
- ✅ Generates unique payment reference: `monthly_{subscription_id}`

### **Test Case 3: Yearly Paid Subscription**

**Description:** Create a new user with yearly paid subscription and auto-generated workspace

**Request:**

```bash
curl -X 'POST' \
  'http://localhost:5001/api/v1/admin/users/create' \
  -H 'Authorization: Bearer {admin_token}' \
  -H 'Content-Type: application/json' \
  -d '{
    "name": "Bob Wilson",
    "email": "<EMAIL>",
    "plan_id": "8437438a-55be-408a-8c2d-f950145ec5ad",
    "is_trial": false,
    "billing_cycle": "yearly",
    "payment_remarks": "Yearly subscription with discount"
  }'
```

**What Happens:**

- ✅ Creates user with PENDING status
- ✅ Auto-generates workspace: `bob_wilson_workspace`
- ✅ Creates 365-day yearly subscription
- ✅ Sets payment amount to plan's yearly price
- ✅ Generates unique payment reference: `yearly_{subscription_id}`
