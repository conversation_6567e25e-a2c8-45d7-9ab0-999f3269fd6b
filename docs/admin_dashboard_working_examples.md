# Admin Dashboard Working Examples

This document provides comprehensive examples of all admin dashboard API endpoints.

## Table of Contents

1. [Setup and Authentication](#setup-and-authentication)
2. [Admin User Creation](#admin-user-creation)
3. [Workspace Management](#workspace-management)
4. [Subscription Management](#subscription-management)
5. [Error Handling Examples](#error-handling-examples)

## Setup and Authentication

### Environment Variables

```bash
export ADMIN_TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************.XT8N7Zrbofr7wrmLBpRyqUpVsFlPC46kHt6cK_ynftY"  # pragma: allowlist secret
export BASE_URL="http://127.0.0.1:5001/api"
export BASIC_PLAN_ID="8437438a-55be-408a-8c2d-f950145ec5ad"
export PRO_PLAN_ID="870f787d-d460-49f0-bb1d-b1001009862b"
```

### Health Check

**Request**:

```bash
curl -X GET "http://127.0.0.1:5001/api/v1/health/"
```

**Response**:

```json
{
  "status": "healthy",
  "message": "Service is running normally",
  "version": "0.1.0"
}
```

## Admin User Creation

### Create Trial User

**Request**:

```bash
curl -X POST "http://127.0.0.1:5001/api/v1/admin/users/create" \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "user_email": "<EMAIL>",
    "user_name": "Test Trial User",
    "user_password": "TestPassword123!",  # pragma: allowlist secret
    "workspace_name": null,
    "plan_id": "8437438a-55be-408a-8c2d-f950145ec5ad",
    "billing_cycle": "trial",
    "is_trial": true
  }'
```

**Response**:

```json
{
  "success": true,
  "message": "User, workspace, and subscription created successfully",
  "user": {
    "id": "02f5cb94-f75e-4dd8-89d8-c130c0d5fbfb",
    "email": "<EMAIL>",
    "name": "Test Trial User",
    "is_active": true,
    "is_admin": false,
    "created_at": "2025-07-31T11:05:22.384531"
  },
  "workspace": {
    "id": "3f89a69c-2737-4dd8-8e9e-bf2294154fa9",
    "name": "test_trial_user_workspace",
    "status": "active",
    "created_at": "2025-07-31T11:05:22.384531"
  },
  "subscription": {
    "id": "c3e91d86-6f4a-441d-ac8c-90dc3dacf211",
    "plan_id": "8437438a-55be-408a-8c2d-f950145ec5ad",
    "plan_name": "Basic",
    "billing_cycle": "trial",
    "status": "active",
    "is_trial": true,
    "start_date": "2025-07-31",
    "end_date": "2025-08-30"
  }
}
```

## Workspace Management

### List All Workspaces

**Request**:

```bash
curl -X GET "http://127.0.0.1:5001/api/v1/admin/workspaces/" \
  -H "Authorization: Bearer $ADMIN_TOKEN"
```

**Response**:

```json
{
  "success": true,
  "total_count": 6,
  "active_count": 6,
  "trial_count": 1,
  "expired_count": 0,
  "workspaces": [
    {
      "workspace_id": "3f89a69c-2737-4dd8-8e9e-bf2294154fa9",
      "workspace_name": "test_trial_user_workspace",
      "workspace_status": "active",
      "created_at": "2025-07-31T11:05:22.384531",
      "owner_id": "02f5cb94-f75e-4dd8-89d8-c130c0d5fbfb",
      "owner_name": "Test Trial User",
      "owner_email": "<EMAIL>",
      "subscription_id": "c3e91d86-6f4a-441d-ac8c-90dc3dacf211",
      "plan_name": "Basic",
      "billing_cycle": "trial",
      "subscription_status": "active",
      "is_trial": true,
      "subscription_end_date": "2025-08-30",
      "days_until_expiry": 30,
      "total_members": 1
    }
  ]
}
```

## Subscription Management

### Convert Trial to Paid

**Request**:

```bash
curl -X PUT "http://127.0.0.1:5001/api/v1/admin/workspaces/3f89a69c-2737-4dd8-8e9e-bf2294154fa9/subscription" \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "action": "trial_to_paid",
    "billing_cycle": "monthly",
    "remarks": "Converted trial to monthly paid subscription"
  }'
```

**Response**:

```json
{
  "success": true,
  "message": "Successfully converted trial to paid subscription for workspace test_trial_user_workspace",
  "workspace_id": "3f89a69c-2737-4dd8-8e9e-bf2294154fa9",
  "workspace_name": "test_trial_user_workspace",
  "action_performed": "converted_trial_to_paid",
  "performed_by": "073b8a90-4f1f-475d-8195-49db5cb61935",
  "performed_at": "2025-07-31T16:55:16.060434",
  "subscription_details": {
    "subscription_id": "c3e91d86-6f4a-441d-ac8c-90dc3dacf211",
    "plan_id": "8437438a-55be-408a-8c2d-f950145ec5ad",
    "plan_name": "Basic",
    "billing_cycle": "monthly",
    "status": "active",
    "is_trial": false,
    "start_date": "2025-07-31",
    "end_date": "2025-08-30",
    "days_remaining": 30
  },
  "payment_details": {
    "payment_id": "11111111-1111-1111-1111-111111111111",
    "amount": "100.0",
    "reference": "admin_trial_to_paid_c3e91d86_20250731_165516",
    "paid_at": "2025-07-31"
  }
}
```

### Cancel Subscription

**Request**:

```bash
curl -X DELETE "http://127.0.0.1:5001/api/v1/admin/workspaces/6cadd6a4-ed19-47af-87ce-700fdb0b0c8e/subscription" \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "cancel_immediately": false,
    "cancellation_reason": "Customer requested cancellation",
    "refund_amount": 50.00,
    "refund_reference": "REFUND_12345678"
  }'
```

**Response**:

```json
{
  "success": true,
  "message": "Successfully cancelled subscription for workspace another_trial_user_workspace",
  "workspace_id": "6cadd6a4-ed19-47af-87ce-700fdb0b0c8e",
  "workspace_name": "another_trial_user_workspace",
  "action_performed": "cancelled",
  "performed_by": "073b8a90-4f1f-475d-8195-49db5cb61935",
  "performed_at": "2025-07-31T17:19:24.181565",
  "subscription_details": {
    "subscription_id": "1563302a-e33e-400f-85de-0d259440f58c",
    "plan_id": "8437438a-55be-408a-8c2d-f950145ec5ad",
    "plan_name": "Basic",
    "billing_cycle": "monthly",
    "status": "cancelled",
    "is_trial": false,
    "is_current": false,
    "start_date": "2025-07-31",
    "end_date": "2025-08-30",
    "days_remaining": 30
  },
  "payment_details": {
    "payment_id": "6517f1f2-65af-472b-b72a-97afbbd023e9",
    "amount": "-50.0",
    "reference": "refund_1563302a-e33e-400f-85de-0d259440f58c",
    "paid_at": "2025-07-31"
  }
}
```

## Error Handling Examples

### Invalid UUID Format

**Request**:

```bash
curl -X GET "http://127.0.0.1:5001/api/v1/admin/workspaces/invalid-uuid" \
  -H "Authorization: Bearer $ADMIN_TOKEN"
```

**Response**:

```json
{
  "detail": "[VALIDATION_ERROR] Invalid workspace ID format (Context: {'workspace_id': 'invalid-uuid', 'error': 'Invalid UUID format'})"
}
```

### Missing Authentication Token

**Request**:

```bash
curl -X GET "http://127.0.0.1:5001/api/v1/admin/workspaces/"
```

**Response**:

```json
{
  "detail": "Not authenticated"
}
```

## Summary

This documentation covers the major admin dashboard functionality including user creation, workspace management, subscription management, and error handling. All endpoints include proper authentication, validation, and audit logging.
