# Admin Workspace Management - Complete Guide

## Overview

The Admin Workspace Management system provides comprehensive workspace and subscription management for administrators. This system implements a **clean, action-based approach** with complete historical tracking and supports all subscription lifecycle scenarios.

## 🎯 **Core Design Principles**

### **Single Subscription Per Workspace**

- ✅ **Always UPDATE existing subscription** (never create new ones)
- ✅ **Complete audit trail** via audit logs and payment records
- ✅ **Clean data model** with no subscription chains
- ✅ **Easy queries** - always query `is_current = true`

### **Action-Based Updates**

- ✅ **Explicit actions** instead of complex change types
- ✅ **Clear intent** - each action has a specific purpose
- ✅ **Flexible parameters** - optional fields based on action type
- ✅ **Comprehensive validation** - action-specific requirements

## 📋 **API Endpoints**

| Method | Endpoint                              | Purpose                          |
| ------ | ------------------------------------- | -------------------------------- |
| GET    | `/admin/workspaces`                   | List all workspaces with filters |
| GET    | `/admin/workspaces/{id}`              | Get detailed workspace info      |
| PUT    | `/admin/workspaces/{id}/subscription` | Update subscription (unified)    |
| DELETE | `/admin/workspaces/{id}/subscription` | Cancel subscription              |
| PATCH  | `/admin/workspaces/{id}/status`       | Enable/archive workspace         |
| GET    | `/admin/workspaces/trials`            | List trial workspaces            |
| GET    | `/admin/workspaces/expiring-soon`     | List expiring workspaces         |

## 🔍 **1. List All Workspaces**

### **Endpoint**

```http
GET /api/v1/admin/workspaces
Authorization: Bearer {admin_token}
```

### **Essential Filters**

- `query` - Search workspace name or owner email
- `status` - Filter by workspace status (active/archived)
- `sort_by` - Sort field (default: created_at)
- `sort_order` - Sort order: asc/desc (default: desc)

**Note:** For expiring workspaces, use the dedicated `/admin/workspaces/expiring` endpoint.

- Pagination via existing pagination service

### **Example Requests**

```bash
# List all workspaces
GET /admin/workspaces

# Search by name or email
GET /admin/workspaces?query=acme

# Show active workspaces only
GET /admin/workspaces?status=active

# Show expiring soon
GET /admin/workspaces?expires_soon=true

# Paginated results
GET /admin/workspaces?page=2&page_size=25
```

### **Response**

```json
{
  "success": true,
  "total_count": 150,
  "active_count": 120,
  "trial_count": 25,
  "expired_count": 5,
  "workspaces": [
    {
      "workspace_id": "uuid",
      "workspace_name": "Acme Corp Workspace",
      "workspace_status": "active",
      "created_at": "2024-01-01T00:00:00Z",
      "owner_name": "John Doe",
      "owner_email": "<EMAIL>",
      "plan_name": "Professional",
      "billing_cycle": "monthly",
      "subscription_status": "active",
      "is_trial": false,
      "subscription_end_date": "2024-02-01",
      "days_until_expiry": 15,
      "total_members": 5
    }
  ]
}
```

## 🔍 **2. Get Workspace Details**

### **Endpoint**

```http
GET /api/v1/admin/workspaces/{workspace_id}
Authorization: Bearer {admin_token}
```

### **Response**

```json
{
  "workspace_id": "uuid",
  "workspace_name": "Acme Corp Workspace",
  "workspace_status": "active",
  "created_at": "2024-01-01T00:00:00Z",
  "owner_name": "John Doe",
  "owner_email": "<EMAIL>",
  "subscription": {
    "subscription_id": "uuid",
    "plan_name": "Professional",
    "billing_cycle": "monthly",
    "status": "active",
    "is_trial": false,
    "start_date": "2024-01-01",
    "end_date": "2024-02-01",
    "days_remaining": 15
  },
  "members": [
    {
      "user_name": "Jane Smith",
      "user_email": "<EMAIL>",
      "role": "member",
      "status": "active"
    }
  ],
  "payment_history": [
    {
      "amount": "29.99",
      "reference": "admin_upgrade_plan_12345678",
      "paid_at": "2024-01-01",
      "type": "payment"
    }
  ],
  "recent_activity": [
    {
      "action": "subscription_updated",
      "timestamp": "2024-01-01T00:00:00Z",
      "actor_id": "admin-uuid",
      "success": true
    }
  ]
}
```

## 🔄 **3. Update Workspace Subscription (Unified)**

### **Endpoint**

```http
PUT /api/v1/admin/workspaces/{workspace_id}/subscription
Authorization: Bearer {admin_token}
Content-Type: application/json
```

### **Action-Based Request Format**

All subscription updates use the same endpoint with different `action` values:

#### **Action 1: Renew Subscription**

```json
{
  "action": "renew",
  "extend_months": 3,
  "remarks": "3-month renewal for client"
}
```

**What happens:**

- Extends `end_date` by specified months
- Creates payment record
- Keeps current plan and billing cycle

#### **Action 2: Upgrade Plan**

```json
{
  "action": "upgrade_plan",
  "plan_id": "premium-plan-uuid",
  "billing_cycle": "yearly",
  "remarks": "Upgrade to premium yearly"
}
```

**What happens:**

- Updates `plan_id` to new plan
- Optionally updates `billing_cycle` if provided
- Keeps current end_date unless billing cycle changes
- Creates payment record for plan difference

#### **Action 3: Upgrade Plan + Change Billing**

```json
{
  "action": "upgrade_plan",
  "plan_id": "premium-plan-uuid",
  "billing_cycle": "yearly",
  "remarks": "Upgrade to premium yearly"
}
```

**What happens:**

- Updates `plan_id` to new plan
- Changes `billing_cycle` to yearly
- Recalculates `end_date` (today + 365 days)
- Creates payment record for yearly amount

#### **Action 4: Trial to Paid (Monthly)**

```json
{
  "action": "upgrade_plan",
  "plan_id": "plan-uuid",
  "billing_cycle": "monthly",
  "remarks": "Converting trial to monthly"
}
```

**What happens:**

- Sets `is_trial = false`
- Updates `plan_id` (if provided)
- Sets `billing_cycle = monthly`
- Sets `end_date = today + 30 days`
- Creates payment record

#### **Action 5: Trial to Paid (Yearly)**

```json
{
  "action": "upgrade_plan",
  "plan_id": "plan-uuid",
  "billing_cycle": "yearly",
  "remarks": "Converting trial directly to yearly"
}
```

**What happens:**

- Sets `is_trial = false`
- Updates `plan_id` (if provided)
- Sets `billing_cycle = yearly`
- Sets `end_date = today + 365 days`
- Creates payment record for yearly amount

#### **Action 6: Change Billing Cycle Only**

```json
{
  "action": "change_billing",
  "billing_cycle": "yearly",
  "remarks": "Switching to yearly billing for discount"
}
```

**What happens:**

- Changes `billing_cycle` to yearly
- Recalculates `end_date` (today + 365 days)
- Keeps current plan
- Creates payment record for new billing cycle

### **Response**

```json
{
  "success": true,
  "message": "Successfully upgrade_plan subscription for workspace Acme Corp",
  "workspace_id": "uuid",
  "workspace_name": "Acme Corp Workspace",
  "action_performed": "upgrade_plan",
  "performed_by": "admin-uuid",
  "performed_at": "2024-01-01T00:00:00Z",
  "subscription_details": {
    "subscription_id": "uuid",
    "plan_name": "Premium",
    "billing_cycle": "yearly",
    "end_date": "2024-12-31"
  },
  "payment_details": {
    "payment_id": "uuid",
    "amount": "299.99",
    "reference": "admin_upgrade_plan_12345678",
    "paid_at": "2024-01-01"
  }
}
```

## 🚫 **4. Cancel Workspace Subscription**

### **Endpoint**

```http
DELETE /api/v1/admin/workspaces/{workspace_id}/subscription
Authorization: Bearer {admin_token}
Content-Type: application/json
```

### **Request**

```json
{
  "cancel_immediately": true,
  "cancellation_reason": "Customer request - moving to competitor",
  "refund_amount": 50.0,
  "refund_reference": "REFUND_2024_001"
}
```

### **What Happens**

- Sets `subscription.status = CANCELLED`
- If `cancel_immediately = true`: sets `end_date = today`
- Creates refund payment record (if refund_amount provided)
- Logs cancellation in audit trail

## 🔧 **5. Update Workspace Status**

### **Endpoint**

```http
PATCH /api/v1/admin/workspaces/{workspace_id}/status
Authorization: Bearer {admin_token}
Content-Type: application/json
```

### **Request**

```json
{
  "status": "archived",
  "reason": "Customer requested workspace archival"
}
```

### **What Happens**

- Updates `workspace.status = ARCHIVED` (or ACTIVE)
- Preserves subscription and all data
- Logs status change in audit trail

## 🔗 **6. Convenience Endpoints**

### **Get Trial Workspaces**

```http
GET /api/v1/admin/workspaces/trials
```

Returns all workspaces with trial subscriptions.

### **Get Expiring Workspaces**

```http
GET /api/v1/admin/workspaces/expiring-soon
```

Returns workspaces expiring within 7 days.

## 📊 **Complete Scenario Examples**

### **Scenario 1: Monthly → Yearly Upgrade**

```bash
curl -X 'PUT' \
  'http://localhost:5001/api/v1/admin/workspaces/{workspace_id}/subscription' \
  -H 'Authorization: Bearer {admin_token}' \
  -H 'Content-Type: application/json' \
  -d '{
    "action": "upgrade_plan",
    "plan_id": "same-or-new-plan-uuid",
    "billing_cycle": "yearly",
    "remarks": "Upgrading to yearly for discount"
  }'
```

### **Scenario 2: Trial → Yearly Direct**

```bash
curl -X 'PUT' \
  'http://localhost:5001/api/v1/admin/workspaces/{workspace_id}/subscription' \
  -H 'Authorization: Bearer {admin_token}' \
  -H 'Content-Type: application/json' \
  -d '{
    "action": "upgrade_plan",
    "plan_id": "premium-plan-uuid",
    "billing_cycle": "yearly",
    "remarks": "Converting trial directly to yearly premium"
  }'
```

### **Scenario 3: Simple Plan Upgrade**

```bash
curl -X 'PUT' \
  'http://localhost:5001/api/v1/admin/workspaces/{workspace_id}/subscription' \
  -H 'Authorization: Bearer {admin_token}' \
  -H 'Content-Type: application/json' \
  -d '{
    "action": "upgrade_plan",
    "plan_id": "premium-plan-uuid",
    "remarks": "Upgrading to premium plan"
  }'
```

### **Scenario 4: 3-Month Renewal**

```bash
curl -X 'PUT' \
  'http://localhost:5001/api/v1/admin/workspaces/{workspace_id}/subscription' \
  -H 'Authorization: Bearer {admin_token}' \
  -H 'Content-Type: application/json' \
  -d '{
    "action": "renew",
    "extend_months": 3,
    "remarks": "3-month renewal for client"
  }'
```

### **Scenario 5: Billing Cycle Change Only**

```bash
curl -X 'PUT' \
  'http://localhost:5001/api/v1/admin/workspaces/{workspace_id}/subscription' \
  -H 'Authorization: Bearer {admin_token}' \
  -H 'Content-Type: application/json' \
  -d '{
    "action": "change_billing",
    "billing_cycle": "yearly",
    "remarks": "Switching to yearly billing"
  }'
```

## 🔍 **Database Operations**

### **What Gets Updated**

For every subscription change:

1. **Subscription Table** - Always UPDATE existing record:

   ```sql
   UPDATE subscriptions
   SET plan_id = ?, billing_cycle = ?, end_date = ?, is_trial = ?, status = 'ACTIVE'
   WHERE workspace_id = ? AND is_current = true;
   ```

2. **Payment Table** - Always CREATE new record:

   ```sql
   INSERT INTO payments (subscription_id, amount_paid, reference, remarks, created_by)
   VALUES (?, ?, ?, ?, ?);
   ```

3. **Audit Log Table** - Always CREATE new record:
   ```sql
   INSERT INTO audit_logs (action, target_id, target_type, actor_id, remarks)
   VALUES ('subscription_updated', ?, 'subscription', ?, ?);
   ```

### **Historical Tracking**

- **Payment History**: Every change creates a payment record
- **Audit Trail**: Complete log of who changed what when
- **State Snapshots**: Before/after states captured in audit details
- **Admin Attribution**: All changes linked to admin user

## ✅ **Production Features**

### **Complete Functionality**

- ✅ **List all workspaces** with essential filters and search
- ✅ **Detailed workspace view** with complete history
- ✅ **Flexible subscription updates** with action-based approach
- ✅ **Subscription cancellation** with optional refunds
- ✅ **Workspace status management** (active/archived)
- ✅ **Convenience endpoints** for common operations

### **Robust Architecture**

- ✅ **Single subscription per workspace** - clean data model
- ✅ **Always UPDATE approach** - no subscription chains
- ✅ **Complete audit trail** - full compliance tracking
- ✅ **Action-based updates** - clear, explicit operations
- ✅ **Comprehensive validation** - prevents invalid operations
- ✅ **Error handling** - graceful failure with rollback

### **Integration**

- ✅ **Existing pagination service** - efficient large dataset handling
- ✅ **Existing validation system** - consistent field validation
- ✅ **Database relationships** - manual joins for missing relationships
- ✅ **Authentication system** - admin-only access with JWT
- ✅ **Audit logging** - complete compliance tracking

## 🚀 **Benefits Summary**

1. **Single Source of Truth** - One admin interface for all operations
2. **Clean API Design** - RESTful endpoints with clear purposes
3. **Flexible Operations** - Handles all subscription scenarios
4. **Complete History** - Full audit trail and payment tracking
5. **Production Ready** - Comprehensive validation and error handling
6. **Easy Maintenance** - Simple, consistent patterns throughout

**The admin workspace management system provides a complete, production-ready solution for managing all workspace and subscription operations from a centralized admin interface.**
