"""
Unit tests for the pagination system.

Tests the reusable pagination utilities, dependencies, and models.
"""

import pytest
from sqlmodel import select

from src.entities.plan import Plan
from src.models.pagination import PaginationConfig, PaginationMeta
from src.utils.pagination import (
    PaginationService,
    paginate_sqlmodel_query,
)


@pytest.mark.unit
class TestPaginationConfig:
    """Test pagination configuration."""

    def test_default_config(self):
        """Test default pagination configuration."""
        config = PaginationConfig()
        assert config.default_page_size == 20
        assert config.max_page_size == 100
        assert config.min_page_size == 1
        assert config.default_page == 1

    def test_predefined_configs(self):
        """Test predefined pagination configurations."""
        # Test that we can create configs with different settings
        standard_config = PaginationConfig(default_page_size=20, max_page_size=100)
        assert standard_config.default_page_size == 20
        assert standard_config.max_page_size == 100

        # Large dataset config
        large_config = PaginationConfig(default_page_size=50, max_page_size=500)
        assert large_config.default_page_size == 50
        assert large_config.max_page_size == 500


@pytest.mark.unit
class TestPaginationMeta:
    """Test pagination metadata model."""

    def test_create_pagination_meta(self):
        """Test creating pagination metadata."""
        meta = PaginationMeta.create(page=2, page_size=10, total_items=25)

        assert meta.page == 2
        assert meta.page_size == 10
        assert meta.total_items == 25
        assert meta.total_pages == 3
        assert meta.has_next is True
        assert meta.has_previous is True
        assert meta.next_page == 3
        assert meta.previous_page == 1

    def test_first_page_pagination_meta(self):
        """Test pagination metadata for first page."""
        meta = PaginationMeta.create(page=1, page_size=10, total_items=25)

        assert meta.page == 1
        assert meta.has_next is True
        assert meta.has_previous is False
        assert meta.next_page == 2
        assert meta.previous_page is None

    def test_empty_pagination_meta(self):
        """Test pagination metadata with no items."""
        meta = PaginationMeta.create(page=1, page_size=10, total_items=0)

        assert meta.page == 1
        assert meta.total_items == 0
        assert meta.total_pages == 0
        assert meta.has_next is False
        assert meta.has_previous is False


@pytest.mark.unit
class TestPaginationService:
    """Test pagination service."""

    def test_validate_params_success(self, test_db_session):
        """Test successful parameter validation."""
        service = PaginationService(test_db_session)
        page, page_size = service.validate_params(2, 20)

        assert page == 2
        assert page_size == 20

    def test_validate_params_invalid_page(self, test_db_session):
        """Test parameter validation normalizes invalid page."""
        service = PaginationService(test_db_session)

        # Invalid page gets normalized to 1
        page, page_size = service.validate_params(0, 20)
        assert page == 1
        assert page_size == 20

    def test_paginate_query_with_plans(self, test_db_session, bulk_plans):
        """Test paginating a query with plan data."""
        service = PaginationService(test_db_session)
        query = select(Plan).order_by(Plan.created_at.desc())

        plans, pagination_meta = service.paginate_query(query, page=1, page_size=10)

        assert len(plans) == 10
        assert pagination_meta.page == 1
        assert pagination_meta.page_size == 10
        assert pagination_meta.total_items == 25
        assert pagination_meta.total_pages == 3
        assert pagination_meta.has_next is True
        assert pagination_meta.has_previous is False

    def test_paginate_query_last_page(self, test_db_session, bulk_plans):
        """Test paginating last page."""
        service = PaginationService(test_db_session)
        query = select(Plan).order_by(Plan.created_at.desc())

        plans, pagination_meta = service.paginate_query(query, page=3, page_size=10)

        assert len(plans) == 5  # Remaining items
        assert pagination_meta.page == 3
        assert pagination_meta.has_next is False
        assert pagination_meta.has_previous is True


@pytest.mark.unit
class TestPaginationUtilityFunctions:
    """Test pagination utility functions."""

    def test_paginate_sqlmodel_query(self, test_db_session, bulk_plans):
        """Test the convenience function for paginating SQLModel queries."""
        query = select(Plan).order_by(Plan.created_at.desc())

        plans, pagination_meta = paginate_sqlmodel_query(test_db_session, query, page=1, page_size=10)

        assert len(plans) == 10
        assert pagination_meta.page == 1
        assert pagination_meta.total_items == 25

    def test_paginate_empty_result(self, test_db_session):
        """Test paginating empty query result."""
        from uuid import uuid4

        non_existent_id = uuid4()
        query = select(Plan).where(Plan.id == non_existent_id)

        plans, pagination_meta = paginate_sqlmodel_query(test_db_session, query, page=1, page_size=10)

        assert len(plans) == 0
        assert pagination_meta.total_items == 0
        assert pagination_meta.total_pages == 0
