"""Unit tests for plan service."""

import uuid

import pytest
from pydantic import ValidationError
from sqlmodel import select

from src.entities.plan import Plan
from src.exceptions import AccountError
from src.models.plan import PlanCreateRequest, PlanUpdateRequest
from src.services.plan_service import PlanService, get_plan_service


class TestPlanService:
    """Test cases for PlanService."""

    def test_get_plan_service(self, test_db_session):
        """Test get_plan_service factory function."""
        service = get_plan_service(test_db_session)
        assert isinstance(service, PlanService)
        assert service.db_session == test_db_session

    def test_create_plan_success(self, test_db_session, created_test_admin, valid_plan_data):
        """Test successful plan creation."""
        service = PlanService(test_db_session)
        request = PlanCreateRequest(**valid_plan_data)

        plan = service.create_plan(request, created_by=created_test_admin.id)

        assert plan.name == valid_plan_data["name"]
        assert plan.plan_code == valid_plan_data["plan_code"]
        assert plan.description == valid_plan_data["description"]
        assert plan.price_per_month == valid_plan_data["price_per_month"]
        assert plan.is_active == valid_plan_data["is_active"]
        assert plan.is_public == valid_plan_data["is_public"]
        assert plan.created_by == created_test_admin.id
        assert plan.id is not None

        # Verify plan is in database
        db_plan = test_db_session.exec(select(Plan).where(Plan.id == plan.id)).first()
        assert db_plan is not None
        assert db_plan.plan_code == valid_plan_data["plan_code"]

    def test_create_plan_minimal_data(self, test_db_session, created_test_admin, minimal_plan_data):
        """Test plan creation with minimal required data."""
        service = PlanService(test_db_session)
        request = PlanCreateRequest(**minimal_plan_data)

        plan = service.create_plan(request, created_by=created_test_admin.id)

        assert plan.name == minimal_plan_data["name"]
        assert plan.plan_code == minimal_plan_data["plan_code"]
        assert plan.description is None
        assert plan.price_per_month == 9.99  # Updated to match fixture
        assert plan.price_per_year is None
        assert plan.is_active is True  # Default value
        assert plan.is_public is True  # Default value

    def test_create_plan_trial_validation_missing_duration(self, test_db_session, created_test_admin):
        """Test plan creation fails when trial is available but duration is missing."""
        # This validation now happens at Pydantic level
        request_data = {
            "name": "Trial Plan",
            "plan_code": "TRIAL_PLAN",
            "price_per_month": 19.99,
            "trial_available": True,
            # Missing trial_duration_days
            "features": {},
        }

        with pytest.raises(ValidationError):
            PlanCreateRequest(**request_data)

    def test_create_plan_trial_validation_duration_without_trial(self, test_db_session, created_test_admin):
        """Test plan creation fails when trial duration is set but trial is not available."""
        # The service layer still validates this case
        service = PlanService(test_db_session)
        request_data = {
            "name": "No Trial Plan",
            "plan_code": "NO_TRIAL_PLAN",
            "price_per_month": 29.99,
            "trial_available": False,
            "trial_duration_days": 30,  # Service layer rejects this
            "features": {},
        }

        request = PlanCreateRequest(**request_data)

        with pytest.raises(AccountError) as exc_info:
            service.create_plan(request, created_by=created_test_admin.id)

        assert exc_info.value.error_code == "TRIAL_DURATION_NOT_ALLOWED"

    def test_create_plan_trial_duration_zero_allowed(self, test_db_session, created_test_admin):
        """Test plan creation allows trial_duration_days=0 when trial_available=false."""
        # Zero is allowed because the service layer only rejects > 0
        service = PlanService(test_db_session)
        request_data = {
            "name": "No Trial Plan",
            "plan_code": "NO_TRIAL_PLAN_ZERO",
            "price_per_month": 29.99,
            "trial_available": False,
            "trial_duration_days": 0,  # Zero is allowed
            "features": {},
        }

        request = PlanCreateRequest(**request_data)
        plan = service.create_plan(request, created_by=created_test_admin.id)

        # Should succeed and convert 0 to None
        assert plan.trial_available is False
        assert plan.trial_duration_days is None  # Service converts 0 to None

    def test_create_plan_duplicate_plan_code(self, test_db_session, created_test_admin, valid_plan_data):
        """Test plan creation fails with duplicate plan_code."""
        service = PlanService(test_db_session)
        request = PlanCreateRequest(**valid_plan_data)

        # Create first plan
        service.create_plan(request, created_by=created_test_admin.id)

        # Try to create second plan with same plan_code
        with pytest.raises(AccountError) as exc_info:
            service.create_plan(request, created_by=created_test_admin.id)

        assert exc_info.value.error_code == "PLAN_CODE_EXISTS"
        assert exc_info.value.status_code == 400

    def test_get_plan_by_id_success(self, test_db_session, sample_plans):
        """Test successful plan retrieval by ID."""
        service = PlanService(test_db_session)
        plan_id = sample_plans["active_public_plan"].id

        plan = service.get_plan_by_id(plan_id)

        assert plan is not None
        assert plan.id == plan_id
        assert plan.name == "Active Public Plan"

    def test_get_plan_by_id_not_found(self, test_db_session):
        """Test plan retrieval by non-existent ID."""
        service = PlanService(test_db_session)
        fake_id = uuid.uuid4()

        plan = service.get_plan_by_id(fake_id)

        assert plan is None

    def test_get_plan_by_id_filtered_active_only(self, test_db_session, sample_plans):
        """Test filtered plan retrieval with active_only filter."""
        service = PlanService(test_db_session)

        # Should find active plan
        active_plan_id = sample_plans["active_public_plan"].id
        plan = service.get_plan_by_id_filtered(active_plan_id, active_only=True)
        assert plan is not None

        # Should not find inactive plan
        inactive_plan_id = sample_plans["inactive_plan"].id
        plan = service.get_plan_by_id_filtered(inactive_plan_id, active_only=True)
        assert plan is None

    def test_get_plan_by_id_filtered_public_only(self, test_db_session, sample_plans):
        """Test filtered plan retrieval with public_only filter."""
        service = PlanService(test_db_session)

        # Should find public plan
        public_plan_id = sample_plans["active_public_plan"].id
        plan = service.get_plan_by_id_filtered(public_plan_id, public_only=True)
        assert plan is not None

        # Should not find private plan
        private_plan_id = sample_plans["private_plan"].id
        plan = service.get_plan_by_id_filtered(private_plan_id, public_only=True)
        assert plan is None

    def test_get_plan_by_id_filtered_both_filters(self, test_db_session, sample_plans):
        """Test filtered plan retrieval with both active_only and public_only filters."""
        service = PlanService(test_db_session)

        # Should find active and public plan
        active_public_plan_id = sample_plans["active_public_plan"].id
        plan = service.get_plan_by_id_filtered(active_public_plan_id, active_only=True, public_only=True)
        assert plan is not None

        # Should not find inactive plan
        inactive_plan_id = sample_plans["inactive_plan"].id
        plan = service.get_plan_by_id_filtered(inactive_plan_id, active_only=True, public_only=True)
        assert plan is None

        # Should not find private plan
        private_plan_id = sample_plans["private_plan"].id
        plan = service.get_plan_by_id_filtered(private_plan_id, active_only=True, public_only=True)
        assert plan is None

    def test_get_plan_by_code_success(self, test_db_session, sample_plans):
        """Test successful plan retrieval by code."""
        service = PlanService(test_db_session)
        plan_code = sample_plans["active_public_plan"].plan_code

        plan = service.get_plan_by_code(plan_code)

        assert plan is not None
        assert plan.plan_code == plan_code
        assert plan.name == "Active Public Plan"

    def test_get_plan_by_code_not_found(self, test_db_session):
        """Test plan retrieval by non-existent code."""
        service = PlanService(test_db_session)

        plan = service.get_plan_by_code("nonexistent-code")

        assert plan is None

    def test_get_plan_by_code_filtered(self, test_db_session, sample_plans):
        """Test filtered plan retrieval by code."""
        service = PlanService(test_db_session)

        # Should find active and public plan
        active_public_code = sample_plans["active_public_plan"].plan_code
        plan = service.get_plan_by_code_filtered(active_public_code, active_only=True, public_only=True)
        assert plan is not None

        # Should not find inactive plan
        inactive_code = sample_plans["inactive_plan"].plan_code
        plan = service.get_plan_by_code_filtered(inactive_code, active_only=True, public_only=True)
        assert plan is None

    def test_list_plans_no_filters(self, test_db_session, sample_plans):
        """Test listing all plans without filters."""
        service = PlanService(test_db_session)

        plans, total = service.list_plans()

        assert total == 4  # All plans
        assert len(plans) == 4

    def test_list_plans_active_only(self, test_db_session, sample_plans):
        """Test listing plans with active_only filter."""
        service = PlanService(test_db_session)

        plans, total = service.list_plans(active_only=True)

        assert total == 2  # Only active plans
        assert len(plans) == 2
        assert all(plan.is_active for plan in plans)

    def test_list_plans_public_only(self, test_db_session, sample_plans):
        """Test listing plans with public_only filter."""
        service = PlanService(test_db_session)

        plans, total = service.list_plans(public_only=True)

        assert total == 2  # Only public plans
        assert len(plans) == 2
        assert all(plan.is_public for plan in plans)

    def test_list_plans_both_filters(self, test_db_session, sample_plans):
        """Test listing plans with both active_only and public_only filters."""
        service = PlanService(test_db_session)

        plans, total = service.list_plans(active_only=True, public_only=True)

        assert total == 1  # Only active and public plans
        assert len(plans) == 1
        assert plans[0].is_active
        assert plans[0].is_public

    def test_list_plans_pagination(self, test_db_session, bulk_plans):
        """Test plan listing with pagination."""
        service = PlanService(test_db_session)

        # First page
        plans, total = service.list_plans(page=1, page_size=10)
        assert total == 25
        assert len(plans) == 10

        # Second page
        plans, total = service.list_plans(page=2, page_size=10)
        assert total == 25
        assert len(plans) == 10

        # Last page
        plans, total = service.list_plans(page=3, page_size=10)
        assert total == 25
        assert len(plans) == 5  # Remaining plans

    def test_update_plan_success(self, test_db_session, sample_plans):
        """Test successful plan update."""
        service = PlanService(test_db_session)
        plan_id = sample_plans["active_public_plan"].id
        update_data = {
            "name": "Updated Plan Name",
            "description": "Updated description",
            "price_per_month": 29.99,
        }
        request = PlanUpdateRequest(**update_data)

        updated_plan = service.update_plan(plan_id, request)

        assert updated_plan.name == "Updated Plan Name"
        assert updated_plan.description == "Updated description"
        assert updated_plan.price_per_month == 29.99
        # Unchanged fields should remain the same
        assert updated_plan.plan_code == sample_plans["active_public_plan"].plan_code

    def test_update_plan_not_found(self, test_db_session):
        """Test update non-existent plan."""
        service = PlanService(test_db_session)
        fake_id = uuid.uuid4()
        request = PlanUpdateRequest(name="Updated Name")

        with pytest.raises(AccountError) as exc_info:
            service.update_plan(fake_id, request)

        assert exc_info.value.status_code == 404

    def test_update_plan_trial_duration_zero_allowed(self, test_db_session, sample_plans):
        """Test plan update allows trial_duration_days=0 when trial_available=false."""
        service = PlanService(test_db_session)
        plan_id = sample_plans["active_public_plan"].id

        # Update plan to disable trial with duration 0
        update_data = {
            "trial_available": False,
            "trial_duration_days": 0,  # Should be allowed and converted to None
        }
        update_request = PlanUpdateRequest(**update_data)

        # Should not raise an exception
        updated_plan = service.update_plan(plan_id, update_request)

        assert updated_plan.trial_available is False
        assert updated_plan.trial_duration_days is None  # Should be converted to None

    def test_delete_plan_success(self, test_db_session, sample_plans):
        """Test successful plan deletion (soft delete)."""
        service = PlanService(test_db_session)
        plan_id = sample_plans["active_public_plan"].id

        service.delete_plan(plan_id)

        # Plan should still exist but be inactive
        plan = test_db_session.exec(select(Plan).where(Plan.id == plan_id)).first()
        assert plan is not None
        assert not plan.is_active

    def test_delete_plan_not_found(self, test_db_session):
        """Test delete non-existent plan."""
        service = PlanService(test_db_session)
        fake_id = uuid.uuid4()

        with pytest.raises(AccountError) as exc_info:
            service.delete_plan(fake_id)

        assert exc_info.value.status_code == 404
