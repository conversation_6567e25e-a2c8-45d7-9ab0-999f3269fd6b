"""Integration tests for plan functionality."""

from fastapi import status
from sqlmodel import select

from src.entities.plan import Plan


class TestPlanIntegration:
    """Integration tests for plan functionality across the entire stack."""

    def test_complete_plan_lifecycle(self, test_client, test_db_session, created_test_admin, created_test_user):
        """Test complete plan lifecycle from creation to deletion."""
        # Login as admin
        admin_login_data = {
            "email": created_test_admin.email,
            "password": "AdminPassword123!",  # pragma: allowlist secret
            "remember_me": False,
        }
        admin_login_response = test_client.post("/api/v1/auth/login", json=admin_login_data)
        admin_token = admin_login_response.json()["data"]["access_token"]
        admin_headers = {"Authorization": f"Bearer {admin_token}"}

        # Login as regular user
        user_login_data = {
            "email": created_test_user["email"],
            "password": created_test_user["password"],
            "remember_me": False,
        }
        user_login_response = test_client.post("/api/v1/auth/login", json=user_login_data)
        user_token = user_login_response.json()["data"]["access_token"]
        user_headers = {"Authorization": f"Bearer {user_token}"}

        # 1. Create plan
        plan_data = {
            "name": "Lifecycle Test Plan",
            "plan_code": "LIFECYCLE_TEST_PLAN",
            "description": "A plan for testing complete lifecycle",
            "price_per_month": 25.99,
            "is_active": True,
            "is_public": True,
            "features": {"test": True},
        }
        create_response = test_client.post("/api/v1/plans/", json=plan_data, headers=admin_headers)
        assert create_response.status_code == status.HTTP_201_CREATED
        plan_id = create_response.json()["data"]["id"]

        # Verify plan exists in database
        from uuid import UUID

        plan_uuid = UUID(plan_id) if isinstance(plan_id, str) else plan_id
        db_plan = test_db_session.exec(select(Plan).where(Plan.id == plan_uuid)).first()
        assert db_plan is not None
        assert db_plan.created_by == created_test_admin.id

        # 2. Regular user can see the plan
        list_response = test_client.get("/api/v1/plans/", headers=user_headers)
        assert list_response.status_code == status.HTTP_200_OK
        user_plans = list_response.json()["plans"]
        assert any(plan["id"] == plan_id for plan in user_plans)

        # 3. Get plan by ID
        get_response = test_client.get(f"/api/v1/plans/{plan_id}", headers=user_headers)
        assert get_response.status_code == status.HTTP_200_OK
        assert get_response.json()["name"] == "Lifecycle Test Plan"

        # 4. Get plan by code
        get_by_code_response = test_client.get("/api/v1/plans/code/LIFECYCLE_TEST_PLAN", headers=user_headers)
        assert get_by_code_response.status_code == status.HTTP_200_OK
        assert get_by_code_response.json()["plan_code"] == "LIFECYCLE_TEST_PLAN"

        # 5. Update plan
        update_data = {
            "name": "Updated Lifecycle Plan",
            "price_per_month": 35.99,
            "description": "Updated description",
        }
        update_response = test_client.put(f"/api/v1/plans/{plan_id}", json=update_data, headers=admin_headers)
        assert update_response.status_code == status.HTTP_200_OK
        assert update_response.json()["data"]["name"] == "Updated Lifecycle Plan"

        # Verify update in database
        test_db_session.refresh(db_plan)
        assert db_plan.name == "Updated Lifecycle Plan"
        assert db_plan.price_per_month == 35.99

        # 6. Make plan private (user should no longer see it)
        privacy_update = {"is_public": False}
        privacy_response = test_client.put(f"/api/v1/plans/{plan_id}", json=privacy_update, headers=admin_headers)
        assert privacy_response.status_code == status.HTTP_200_OK

        # User can no longer access private plan
        get_private_response = test_client.get(f"/api/v1/plans/{plan_id}", headers=user_headers)
        assert get_private_response.status_code == status.HTTP_404_NOT_FOUND

        # Admin can still access private plan
        admin_get_response = test_client.get(f"/api/v1/plans/{plan_id}", headers=admin_headers)
        assert admin_get_response.status_code == status.HTTP_200_OK

        # 7. Delete plan (soft delete)
        delete_response = test_client.delete(f"/api/v1/plans/{plan_id}", headers=admin_headers)
        assert delete_response.status_code == status.HTTP_200_OK

        # Verify plan is soft deleted (still exists but inactive)
        test_db_session.refresh(db_plan)
        assert db_plan is not None
        assert not db_plan.is_active

        # User cannot access deleted plan
        get_deleted_response = test_client.get(f"/api/v1/plans/{plan_id}", headers=user_headers)
        assert get_deleted_response.status_code == status.HTTP_404_NOT_FOUND

        # Admin can still access deleted plan
        admin_get_deleted_response = test_client.get(f"/api/v1/plans/{plan_id}", headers=admin_headers)
        assert admin_get_deleted_response.status_code == status.HTTP_200_OK

    def test_plan_filtering_across_endpoints(self, test_client, test_db_session, created_test_admin, created_test_user):
        """Test plan filtering consistency across all endpoints."""
        # Login as admin and user
        admin_login_data = {
            "email": created_test_admin.email,
            "password": "AdminPassword123!",  # pragma: allowlist secret
            "remember_me": False,
        }
        admin_login_response = test_client.post("/api/v1/auth/login", json=admin_login_data)
        admin_token = admin_login_response.json()["data"]["access_token"]
        admin_headers = {"Authorization": f"Bearer {admin_token}"}

        user_login_data = {
            "email": created_test_user["email"],
            "password": created_test_user["password"],
            "remember_me": False,
        }
        user_login_response = test_client.post("/api/v1/auth/login", json=user_login_data)
        user_token = user_login_response.json()["data"]["access_token"]
        user_headers = {"Authorization": f"Bearer {user_token}"}

        # Create different types of plans
        plans_data = [
            {
                "name": "Active Public Plan",
                "plan_code": "ACTIVE_PUBLIC",
                "price_per_month": 19.99,
                "is_active": True,
                "is_public": True,
                "features": {"type": "active_public"},
            },
            {
                "name": "Active Private Plan",
                "plan_code": "ACTIVE_PRIVATE",
                "price_per_month": 29.99,
                "is_active": True,
                "is_public": False,
                "features": {"type": "active_private"},
            },
            {
                "name": "Inactive Public Plan",
                "plan_code": "INACTIVE_PUBLIC",
                "price_per_month": 39.99,
                "is_active": False,
                "is_public": True,
                "features": {"type": "inactive_public"},
            },
            {
                "name": "Inactive Private Plan",
                "plan_code": "INACTIVE_PRIVATE",
                "price_per_month": 49.99,
                "is_active": False,
                "is_public": False,
                "features": {"type": "inactive_private"},
            },
        ]

        created_plans = []
        for plan_data in plans_data:
            response = test_client.post("/api/v1/plans/", json=plan_data, headers=admin_headers)
            assert response.status_code == status.HTTP_201_CREATED
            created_plans.append(response.json()["data"])

        # Test list endpoint filtering
        # Admin should see all plans
        admin_list_response = test_client.get("/api/v1/plans/", headers=admin_headers)
        assert admin_list_response.status_code == status.HTTP_200_OK
        admin_plans = admin_list_response.json()["plans"]
        assert len(admin_plans) == 4

        # User should see only active and public plans
        user_list_response = test_client.get("/api/v1/plans/", headers=user_headers)
        assert user_list_response.status_code == status.HTTP_200_OK
        user_plans = user_list_response.json()["plans"]
        assert len(user_plans) == 1
        assert user_plans[0]["plan_code"] == "ACTIVE_PUBLIC"

        # Test get by ID endpoint filtering
        for plan in created_plans:
            plan_id = plan["id"]
            plan_code = plan["plan_code"]

            # Admin can access all plans
            admin_get_response = test_client.get(f"/api/v1/plans/{plan_id}", headers=admin_headers)
            assert admin_get_response.status_code == status.HTTP_200_OK

            # User access depends on plan status
            user_get_response = test_client.get(f"/api/v1/plans/{plan_id}", headers=user_headers)
            if plan_code == "ACTIVE_PUBLIC":
                assert user_get_response.status_code == status.HTTP_200_OK
            else:
                assert user_get_response.status_code == status.HTTP_404_NOT_FOUND

        # Test get by code endpoint filtering
        for plan in created_plans:
            plan_code = plan["plan_code"]

            # Admin can access all plans by code
            admin_get_response = test_client.get(f"/api/v1/plans/code/{plan_code}", headers=admin_headers)
            assert admin_get_response.status_code == status.HTTP_200_OK

            # User access depends on plan status
            user_get_response = test_client.get(f"/api/v1/plans/code/{plan_code}", headers=user_headers)
            if plan_code == "ACTIVE_PUBLIC":
                assert user_get_response.status_code == status.HTTP_200_OK
            else:
                assert user_get_response.status_code == status.HTTP_404_NOT_FOUND

    def test_plan_database_constraints(self, test_client, test_db_session, created_test_admin):
        """Test database constraints and integrity."""
        # Login as admin
        login_data = {
            "email": created_test_admin.email,
            "password": "AdminPassword123!",  # pragma: allowlist secret
            "remember_me": False,
        }
        login_response = test_client.post("/api/v1/auth/login", json=login_data)
        token = login_response.json()["data"]["access_token"]
        headers = {"Authorization": f"Bearer {token}"}

        # Test unique constraint on plan_code
        plan_data = {
            "name": "Unique Test Plan",
            "plan_code": "UNIQUE_CONSTRAINT_TEST",
            "price_per_month": 19.99,
            "features": {"test": True},
        }

        # First plan should succeed
        response1 = test_client.post("/api/v1/plans/", json=plan_data, headers=headers)
        assert response1.status_code == status.HTTP_201_CREATED

        # Second plan with same plan_code should fail
        response2 = test_client.post("/api/v1/plans/", json=plan_data, headers=headers)
        assert response2.status_code == status.HTTP_400_BAD_REQUEST

        # Verify only one plan exists in database
        plans = test_db_session.exec(select(Plan).where(Plan.plan_code == "UNIQUE_CONSTRAINT_TEST")).all()
        assert len(plans) == 1

    def test_plan_audit_trail(self, test_client, test_db_session, created_test_admin):
        """Test plan audit trail and metadata."""
        # Login as admin
        login_data = {
            "email": created_test_admin.email,
            "password": "AdminPassword123!",  # pragma: allowlist secret
            "remember_me": False,
        }
        login_response = test_client.post("/api/v1/auth/login", json=login_data)
        token = login_response.json()["data"]["access_token"]
        headers = {"Authorization": f"Bearer {token}"}

        # Create plan
        plan_data = {
            "name": "Audit Test Plan",
            "plan_code": "AUDIT_TEST_PLAN",
            "price_per_month": 29.99,
            "features": {"audit": True},
        }
        create_response = test_client.post("/api/v1/plans/", json=plan_data, headers=headers)
        assert create_response.status_code == status.HTTP_201_CREATED
        plan_id = create_response.json()["data"]["id"]

        # Verify audit fields
        from uuid import UUID

        plan_uuid = UUID(plan_id) if isinstance(plan_id, str) else plan_id
        db_plan = test_db_session.exec(select(Plan).where(Plan.id == plan_uuid)).first()
        assert db_plan.created_by == created_test_admin.id
        assert db_plan.created_at is not None
        assert db_plan.updated_at is not None
        original_created_at = db_plan.created_at
        original_updated_at = db_plan.updated_at

        # Update plan (add small delay to ensure timestamp difference)
        import time

        time.sleep(0.01)  # 10ms delay to ensure different timestamps
        update_data = {"name": "Updated Audit Plan"}
        update_response = test_client.put(f"/api/v1/plans/{plan_id}", json=update_data, headers=headers)
        assert update_response.status_code == status.HTTP_200_OK

        # Verify audit fields and update worked
        test_db_session.refresh(db_plan)
        assert db_plan.created_by == created_test_admin.id  # Unchanged
        assert db_plan.created_at == original_created_at  # Unchanged
        assert db_plan.name == "Updated Audit Plan"  # Verify update actually happened
        # Note: SQLite truncates microseconds, so we just verify updated_at exists
        assert db_plan.updated_at is not None

    def test_plan_pagination_consistency(self, test_client, test_db_session, created_test_admin, bulk_plans):
        """Test pagination consistency across multiple requests."""
        # Login as admin
        login_data = {
            "email": created_test_admin.email,
            "password": "AdminPassword123!",  # pragma: allowlist secret
            "remember_me": False,
        }
        login_response = test_client.post("/api/v1/auth/login", json=login_data)
        token = login_response.json()["data"]["access_token"]
        headers = {"Authorization": f"Bearer {token}"}

        # Get all plans through pagination
        all_plans = []
        page = 1
        page_size = 5

        while True:
            response = test_client.get(f"/api/v1/plans/?page={page}&page_size={page_size}", headers=headers)
            assert response.status_code == status.HTTP_200_OK
            data = response.json()

            if not data["plans"]:
                break

            all_plans.extend(data["plans"])
            page += 1

            # Safety check to prevent infinite loop
            if page > 10:
                break

        # Verify we got all plans
        assert len(all_plans) == 25  # bulk_plans creates 25 plans

        # Verify no duplicates
        plan_ids = [plan["id"] for plan in all_plans]
        assert len(plan_ids) == len(set(plan_ids))

        # Verify total count consistency
        first_page_response = test_client.get("/api/v1/plans/?page=1&page_size=5", headers=headers)
        assert first_page_response.json()["total"] == 25
