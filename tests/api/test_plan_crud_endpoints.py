"""API tests for plan CRUD endpoints (Update and Delete)."""

import uuid

from fastapi import status


class TestUpdatePlanEndpoint:
    """Test cases for update plan endpoint (PUT /v1/plans/{id})."""

    def test_update_plan_success_admin(self, test_client, created_test_admin, sample_plans):
        """Test successful plan update by admin."""
        # Login as admin
        login_data = {
            "email": created_test_admin.email,
            "password": "AdminPassword123!",  # pragma: allowlist secret
            "remember_me": False,
        }
        login_response = test_client.post("/api/v1/auth/login", json=login_data)
        token = login_response.json()["data"]["access_token"]

        # Update plan
        headers = {"Authorization": f"Bearer {token}"}
        plan_id = sample_plans["active_public_plan"].id
        update_data = {
            "name": "Updated Plan Name",
            "description": "Updated description",
            "price_per_month": 29.99,
            "is_public": False,
        }
        response = test_client.put(f"/api/v1/plans/{plan_id}", json=update_data, headers=headers)

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["result"] == "success"
        assert data["data"]["name"] == "Updated Plan Name"
        assert data["data"]["description"] == "Updated description"
        assert data["data"]["price_per_month"] == 29.99
        assert not data["data"]["is_public"]

    def test_update_plan_partial_update(self, test_client, created_test_admin, sample_plans):
        """Test partial plan update (only some fields)."""
        # Login as admin
        login_data = {
            "email": created_test_admin.email,
            "password": "AdminPassword123!",  # pragma: allowlist secret
            "remember_me": False,
        }
        login_response = test_client.post("/api/v1/auth/login", json=login_data)
        token = login_response.json()["data"]["access_token"]

        # Update only name
        headers = {"Authorization": f"Bearer {token}"}
        plan_id = sample_plans["active_public_plan"].id
        update_data = {"name": "Partially Updated Plan"}
        response = test_client.put(f"/api/v1/plans/{plan_id}", json=update_data, headers=headers)

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["data"]["name"] == "Partially Updated Plan"
        # Other fields should remain unchanged
        assert data["data"]["plan_code"] == sample_plans["active_public_plan"].plan_code

    def test_update_plan_nonexistent(self, test_client, created_test_admin):
        """Test update non-existent plan."""
        # Login as admin
        login_data = {
            "email": created_test_admin.email,
            "password": "AdminPassword123!",  # pragma: allowlist secret
            "remember_me": False,
        }
        login_response = test_client.post("/api/v1/auth/login", json=login_data)
        token = login_response.json()["data"]["access_token"]

        # Try to update non-existent plan
        headers = {"Authorization": f"Bearer {token}"}
        fake_id = str(uuid.uuid4())
        update_data = {"name": "Updated Name"}
        response = test_client.put(f"/api/v1/plans/{fake_id}", json=update_data, headers=headers)

        assert response.status_code == status.HTTP_404_NOT_FOUND

    def test_update_plan_invalid_values(self, test_client, created_test_admin, sample_plans):
        """Test update plan with invalid values."""
        # Login as admin
        login_data = {
            "email": created_test_admin.email,
            "password": "AdminPassword123!",  # pragma: allowlist secret
            "remember_me": False,
        }
        login_response = test_client.post("/api/v1/auth/login", json=login_data)
        token = login_response.json()["data"]["access_token"]

        headers = {"Authorization": f"Bearer {token}"}
        plan_id = sample_plans["active_public_plan"].id

        # Negative price
        update_data = {"price_per_month": -10.0}
        response = test_client.put(f"/api/v1/plans/{plan_id}", json=update_data, headers=headers)
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

        # Empty name
        update_data = {"name": ""}
        response = test_client.put(f"/api/v1/plans/{plan_id}", json=update_data, headers=headers)
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    def test_update_plan_unauthorized_regular_user(self, test_client, created_test_user, sample_plans):
        """Test plan update by regular user (should fail)."""
        # Login as regular user
        login_data = {
            "email": created_test_user["email"],
            "password": created_test_user["password"],
            "remember_me": False,
        }
        login_response = test_client.post("/api/v1/auth/login", json=login_data)
        token = login_response.json()["data"]["access_token"]

        # Try to update plan
        headers = {"Authorization": f"Bearer {token}"}
        plan_id = sample_plans["active_public_plan"].id
        update_data = {"name": "Unauthorized Update"}
        response = test_client.put(f"/api/v1/plans/{plan_id}", json=update_data, headers=headers)

        assert response.status_code == status.HTTP_403_FORBIDDEN

    def test_update_plan_unauthenticated(self, test_client, sample_plans):
        """Test plan update without authentication."""
        plan_id = sample_plans["active_public_plan"].id
        update_data = {"name": "Unauthenticated Update"}
        response = test_client.put(f"/api/v1/plans/{plan_id}", json=update_data)

        assert response.status_code == status.HTTP_401_UNAUTHORIZED


class TestDeletePlanEndpoint:
    """Test cases for delete plan endpoint (DELETE /v1/plans/{id})."""

    def test_delete_plan_success_admin(self, test_client, created_test_admin, sample_plans):
        """Test successful plan deletion (soft delete) by admin."""
        # Login as admin
        login_data = {
            "email": created_test_admin.email,
            "password": "AdminPassword123!",  # pragma: allowlist secret
            "remember_me": False,
        }
        login_response = test_client.post("/api/v1/auth/login", json=login_data)
        token = login_response.json()["data"]["access_token"]

        # Delete plan
        headers = {"Authorization": f"Bearer {token}"}
        plan_id = sample_plans["active_public_plan"].id
        response = test_client.delete(f"/api/v1/plans/{plan_id}", headers=headers)

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["result"] == "success"
        assert data["deleted_plan_id"] == str(plan_id)
        assert "deleted successfully" in data["message"]

        # Verify plan is no longer visible to regular users
        # Login as regular user
        user_login_data = {
            "email": "<EMAIL>",
            "password": "password123",  # pragma: allowlist secret
            "remember_me": False,
        }
        # This would need a created user fixture, but the plan should not be visible

    def test_delete_plan_nonexistent(self, test_client, created_test_admin):
        """Test delete non-existent plan."""
        # Login as admin
        login_data = {
            "email": created_test_admin.email,
            "password": "AdminPassword123!",  # pragma: allowlist secret
            "remember_me": False,
        }
        login_response = test_client.post("/api/v1/auth/login", json=login_data)
        token = login_response.json()["data"]["access_token"]

        # Try to delete non-existent plan
        headers = {"Authorization": f"Bearer {token}"}
        fake_id = str(uuid.uuid4())
        response = test_client.delete(f"/api/v1/plans/{fake_id}", headers=headers)

        assert response.status_code == status.HTTP_404_NOT_FOUND

    def test_delete_plan_unauthorized_regular_user(self, test_client, created_test_user, sample_plans):
        """Test plan deletion by regular user (should fail)."""
        # Login as regular user
        login_data = {
            "email": created_test_user["email"],
            "password": created_test_user["password"],
            "remember_me": False,
        }
        login_response = test_client.post("/api/v1/auth/login", json=login_data)
        token = login_response.json()["data"]["access_token"]

        # Try to delete plan
        headers = {"Authorization": f"Bearer {token}"}
        plan_id = sample_plans["active_public_plan"].id
        response = test_client.delete(f"/api/v1/plans/{plan_id}", headers=headers)

        assert response.status_code == status.HTTP_403_FORBIDDEN

    def test_delete_plan_unauthenticated(self, test_client, sample_plans):
        """Test plan deletion without authentication."""
        plan_id = sample_plans["active_public_plan"].id
        response = test_client.delete(f"/api/v1/plans/{plan_id}")

        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    def test_delete_plan_already_inactive(self, test_client, created_test_admin, sample_plans):
        """Test deleting already inactive plan."""
        # Login as admin
        login_data = {
            "email": created_test_admin.email,
            "password": "AdminPassword123!",  # pragma: allowlist secret
            "remember_me": False,
        }
        login_response = test_client.post("/api/v1/auth/login", json=login_data)
        token = login_response.json()["data"]["access_token"]

        # Try to delete already inactive plan
        headers = {"Authorization": f"Bearer {token}"}
        plan_id = sample_plans["inactive_plan"].id
        response = test_client.delete(f"/api/v1/plans/{plan_id}", headers=headers)

        # Should still succeed (idempotent operation)
        assert response.status_code == status.HTTP_200_OK


class TestPlanValidationAndConstraints:
    """Test cases for plan validation and constraints."""

    def test_plan_code_uniqueness(self, test_client, created_test_admin):
        """Test plan_code uniqueness constraint."""
        # Login as admin
        login_data = {
            "email": created_test_admin.email,
            "password": "AdminPassword123!",  # pragma: allowlist secret
            "remember_me": False,
        }
        login_response = test_client.post("/api/v1/auth/login", json=login_data)
        token = login_response.json()["data"]["access_token"]
        headers = {"Authorization": f"Bearer {token}"}

        # Create first plan
        plan_data = {
            "name": "First Plan",
            "plan_code": "UNIQUE_CODE",
            "price_per_month": 19.99,
            "features": {"feature1": "value1"},
        }
        response = test_client.post("/api/v1/plans/", json=plan_data, headers=headers)
        assert response.status_code == status.HTTP_201_CREATED

        # Try to create second plan with same plan_code
        duplicate_plan_data = {
            "name": "Second Plan",
            "plan_code": "UNIQUE_CODE",  # Same code
            "price_per_month": 29.99,
            "features": {"feature2": "value2"},
        }
        response = test_client.post("/api/v1/plans/", json=duplicate_plan_data, headers=headers)
        assert response.status_code == status.HTTP_400_BAD_REQUEST

    def test_plan_price_validation(self, test_client, created_test_admin):
        """Test price validation (must be >= 0)."""
        # Login as admin
        login_data = {
            "email": created_test_admin.email,
            "password": "AdminPassword123!",  # pragma: allowlist secret
            "remember_me": False,
        }
        login_response = test_client.post("/api/v1/auth/login", json=login_data)
        token = login_response.json()["data"]["access_token"]
        headers = {"Authorization": f"Bearer {token}"}

        # Valid price (0)
        plan_data = {
            "name": "Free Plan",
            "plan_code": "FREE_PLAN",
            "price_per_month": 0.0,
            "features": {},
        }
        response = test_client.post("/api/v1/plans/", json=plan_data, headers=headers)
        assert response.status_code == status.HTTP_201_CREATED

        # Invalid negative price
        plan_data = {
            "name": "Invalid Plan",
            "plan_code": "INVALID_PLAN",
            "price_per_month": -1.0,
            "features": {},
        }
        response = test_client.post("/api/v1/plans/", json=plan_data, headers=headers)
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    def test_plan_name_length_validation(self, test_client, created_test_admin):
        """Test name length validation (max 64 characters)."""
        # Login as admin
        login_data = {
            "email": created_test_admin.email,
            "password": "AdminPassword123!",  # pragma: allowlist secret
            "remember_me": False,
        }
        login_response = test_client.post("/api/v1/auth/login", json=login_data)
        token = login_response.json()["data"]["access_token"]
        headers = {"Authorization": f"Bearer {token}"}

        # Valid max length name (64 characters)
        valid_name = "x" * 64
        plan_data = {
            "name": valid_name,
            "plan_code": "VALID_LENGTH_PLAN",
            "price_per_month": 19.99,
            "features": {},
        }
        response = test_client.post("/api/v1/plans/", json=plan_data, headers=headers)
        assert response.status_code == status.HTTP_201_CREATED

        # Invalid name (65 characters)
        invalid_name = "x" * 65
        plan_data = {
            "name": invalid_name,
            "plan_code": "INVALID_LENGTH_PLAN",
            "price_per_month": 29.99,
            "features": {},
        }
        response = test_client.post("/api/v1/plans/", json=plan_data, headers=headers)
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    def test_plan_description_length_validation(self, test_client, created_test_admin):
        """Test description length validation (max 255 characters)."""
        # Login as admin
        login_data = {
            "email": created_test_admin.email,
            "password": "AdminPassword123!",  # pragma: allowlist secret
            "remember_me": False,
        }
        login_response = test_client.post("/api/v1/auth/login", json=login_data)
        token = login_response.json()["data"]["access_token"]
        headers = {"Authorization": f"Bearer {token}"}

        # Valid max length description (255 characters)
        valid_description = "x" * 255
        plan_data = {
            "name": "Valid Description Plan",
            "plan_code": "VALID_DESC_PLAN",
            "price_per_month": 19.99,
            "description": valid_description,
            "features": {},
        }
        response = test_client.post("/api/v1/plans/", json=plan_data, headers=headers)
        assert response.status_code == status.HTTP_201_CREATED

        # Invalid description (256 characters)
        invalid_description = "x" * 256
        plan_data = {
            "name": "Invalid Description Plan",
            "plan_code": "INVALID_DESC_PLAN",
            "price_per_month": 29.99,
            "description": invalid_description,
            "features": {},
        }
        response = test_client.post("/api/v1/plans/", json=plan_data, headers=headers)
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
