"""Comprehensive tests for admin workspace management API endpoints."""

from uuid import uuid4

from fastapi import status


class TestAdminWorkspaceListingEndpoints:
    """Test workspace listing and filtering endpoints."""

    def test_list_all_workspaces_success(self, test_client, created_test_admin, sample_workspaces):
        """Test successful listing of all workspaces."""
        # Get admin token
        admin_login_response = test_client.post(
            "/api/v1/auth/login",
            json={
                "email": created_test_admin.email,
                "password": "AdminPassword123!",  # pragma: allowlist secret
                "remember_me": False,
            },
        )
        assert admin_login_response.status_code == status.HTTP_200_OK
        admin_token = admin_login_response.json()["data"]["access_token"]
        admin_headers = {"Authorization": f"Bearer {admin_token}"}

        # List all workspaces
        response = test_client.get("/api/v1/admin/workspaces", headers=admin_headers)

        assert response.status_code == status.HTTP_200_OK
        data = response.json()

        # Verify response structure
        assert data["success"] is True
        assert "total_count" in data
        assert "active_count" in data
        assert "trial_count" in data
        assert "expired_count" in data
        assert "workspaces" in data
        assert isinstance(data["workspaces"], list)

        # Verify workspace item structure
        if data["workspaces"]:
            workspace = data["workspaces"][0]
            required_fields = [
                "workspace_id",
                "workspace_name",
                "workspace_status",
                "created_at",
                "owner_name",
                "owner_email",
                "plan_name",
                "billing_cycle",
                "subscription_status",
                "is_trial",
                "subscription_end_date",
                "days_until_expiry",
                "total_members",
            ]
            for field in required_fields:
                assert field in workspace

    def test_list_workspaces_with_search_query(self, test_client, created_test_admin):
        """Test workspace listing with search query."""
        admin_login_response = test_client.post(
            "/api/v1/auth/login",
            json={
                "email": created_test_admin.email,
                "password": "AdminPassword123!",  # pragma: allowlist secret
                "remember_me": False,
            },
        )
        admin_token = admin_login_response.json()["data"]["access_token"]
        admin_headers = {"Authorization": f"Bearer {admin_token}"}

        # Search with query parameter
        response = test_client.get("/api/v1/admin/workspaces?query=test", headers=admin_headers)

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["success"] is True

    def test_list_workspaces_with_status_filter(self, test_client, created_test_admin):
        """Test workspace listing with status filter."""
        admin_login_response = test_client.post(
            "/api/v1/auth/login",
            json={
                "email": created_test_admin.email,
                "password": "AdminPassword123!",  # pragma: allowlist secret
                "remember_me": False,
            },
        )
        admin_token = admin_login_response.json()["data"]["access_token"]
        admin_headers = {"Authorization": f"Bearer {admin_token}"}

        # Filter by active status
        response = test_client.get("/api/v1/admin/workspaces?status=active", headers=admin_headers)

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["success"] is True

    def test_list_workspaces_with_pagination(self, test_client, created_test_admin):
        """Test workspace listing with pagination."""
        admin_login_response = test_client.post(
            "/api/v1/auth/login",
            json={
                "email": created_test_admin.email,
                "password": "AdminPassword123!",  # pragma: allowlist secret
                "remember_me": False,
            },
        )
        admin_token = admin_login_response.json()["data"]["access_token"]
        admin_headers = {"Authorization": f"Bearer {admin_token}"}

        # Test pagination
        response = test_client.get("/api/v1/admin/workspaces?page=1&page_size=10", headers=admin_headers)

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["success"] is True
        assert len(data["workspaces"]) <= 10

    def test_list_workspaces_with_sorting(self, test_client, created_test_admin):
        """Test workspace listing with sorting."""
        admin_login_response = test_client.post(
            "/api/v1/auth/login",
            json={
                "email": created_test_admin.email,
                "password": "AdminPassword123!",  # pragma: allowlist secret
                "remember_me": False,
            },
        )
        admin_token = admin_login_response.json()["data"]["access_token"]
        admin_headers = {"Authorization": f"Bearer {admin_token}"}

        # Test sorting
        response = test_client.get("/api/v1/admin/workspaces?sort_by=created_at&sort_order=asc", headers=admin_headers)

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["success"] is True

    def test_get_trial_workspaces(self, test_client, created_test_admin):
        """Test getting trial workspaces."""
        admin_login_response = test_client.post(
            "/api/v1/auth/login",
            json={
                "email": created_test_admin.email,
                "password": "AdminPassword123!",  # pragma: allowlist secret
                "remember_me": False,
            },
        )
        admin_token = admin_login_response.json()["data"]["access_token"]
        admin_headers = {"Authorization": f"Bearer {admin_token}"}

        # Get trial workspaces
        response = test_client.get("/api/v1/admin/workspaces/trials", headers=admin_headers)

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["success"] is True
        assert "workspaces" in data

    def test_get_expiring_workspaces(self, test_client, created_test_admin):
        """Test getting expiring workspaces."""
        admin_login_response = test_client.post(
            "/api/v1/auth/login",
            json={
                "email": created_test_admin.email,
                "password": "AdminPassword123!",  # pragma: allowlist secret
                "remember_me": False,
            },
        )
        admin_token = admin_login_response.json()["data"]["access_token"]
        admin_headers = {"Authorization": f"Bearer {admin_token}"}

        # Get expiring workspaces
        response = test_client.get("/api/v1/admin/workspaces/expiring", headers=admin_headers)

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["success"] is True
        assert "workspaces" in data


class TestAdminWorkspaceDetailsEndpoint:
    """Test workspace details endpoint."""

    def test_get_workspace_details_success(self, test_client, created_test_admin, sample_workspace_with_subscription):
        """Test successful retrieval of workspace details."""
        admin_login_response = test_client.post(
            "/api/v1/auth/login",
            json={
                "email": created_test_admin.email,
                "password": "AdminPassword123!",  # pragma: allowlist secret
                "remember_me": False,
            },
        )
        admin_token = admin_login_response.json()["data"]["access_token"]
        admin_headers = {"Authorization": f"Bearer {admin_token}"}

        workspace_id = sample_workspace_with_subscription["workspace_id"]

        # Get workspace details
        response = test_client.get(f"/api/v1/admin/workspaces/{workspace_id}", headers=admin_headers)

        assert response.status_code == status.HTTP_200_OK
        data = response.json()

        # Verify response structure
        required_fields = [
            "workspace_id",
            "workspace_name",
            "workspace_status",
            "created_at",
            "owner_name",
            "owner_email",
            "subscription",
            "members",
        ]
        for field in required_fields:
            assert field in data

        # Verify subscription details
        if data["subscription"]:
            subscription_fields = [
                "subscription_id",
                "plan_name",
                "billing_cycle",
                "subscription_status",
                "is_trial",
                "subscription_start_date",
                "subscription_end_date",
                "days_until_expiry",
            ]
            for field in subscription_fields:
                assert field in data["subscription"], f"Missing field: {field}"

    def test_get_workspace_details_invalid_id(self, test_client, created_test_admin):
        """Test workspace details with invalid workspace ID."""
        admin_login_response = test_client.post(
            "/api/v1/auth/login",
            json={
                "email": created_test_admin.email,
                "password": "AdminPassword123!",  # pragma: allowlist secret
                "remember_me": False,
            },
        )
        admin_token = admin_login_response.json()["data"]["access_token"]
        admin_headers = {"Authorization": f"Bearer {admin_token}"}

        # Use invalid UUID
        invalid_id = "invalid-uuid"
        response = test_client.get(f"/api/v1/admin/workspaces/{invalid_id}", headers=admin_headers)

        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    def test_get_workspace_details_nonexistent_id(self, test_client, created_test_admin):
        """Test workspace details with nonexistent workspace ID."""
        admin_login_response = test_client.post(
            "/api/v1/auth/login",
            json={
                "email": created_test_admin.email,
                "password": "AdminPassword123!",  # pragma: allowlist secret
                "remember_me": False,
            },
        )
        admin_token = admin_login_response.json()["data"]["access_token"]
        admin_headers = {"Authorization": f"Bearer {admin_token}"}

        # Use valid but nonexistent UUID
        nonexistent_id = str(uuid4())
        response = test_client.get(f"/api/v1/admin/workspaces/{nonexistent_id}", headers=admin_headers)

        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR


class TestAdminWorkspaceSecurityAndPermissions:
    """Test security and permission controls."""

    def test_regular_user_cannot_list_workspaces(self, test_client, created_test_user):
        """Test that regular users cannot list workspaces."""
        # Get regular user token
        user_login_response = test_client.post(
            "/api/v1/auth/login",
            json={
                "email": created_test_user["email"],
                "password": created_test_user["password"],  # Use the actual password from fixture
                "remember_me": False,
            },
        )
        assert user_login_response.status_code == status.HTTP_200_OK
        user_token = user_login_response.json()["data"]["access_token"]
        user_headers = {"Authorization": f"Bearer {user_token}"}

        # Try to list workspaces as regular user
        response = test_client.get("/api/v1/admin/workspaces", headers=user_headers)
        assert response.status_code == status.HTTP_403_FORBIDDEN

    def test_unauthenticated_user_cannot_list_workspaces(self, test_client):
        """Test that unauthenticated users cannot list workspaces."""
        # Try to list workspaces without authentication
        response = test_client.get("/api/v1/admin/workspaces")
        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    def test_regular_user_cannot_get_workspace_details(self, test_client, created_test_user):
        """Test that regular users cannot get workspace details."""
        user_login_response = test_client.post(
            "/api/v1/auth/login",
            json={
                "email": created_test_user["email"],
                "password": created_test_user["password"],  # Use the actual password from fixture
                "remember_me": False,
            },
        )
        assert user_login_response.status_code == status.HTTP_200_OK
        user_token = user_login_response.json()["data"]["access_token"]
        user_headers = {"Authorization": f"Bearer {user_token}"}

        # Try to get workspace details as regular user
        workspace_id = str(uuid4())
        response = test_client.get(f"/api/v1/admin/workspaces/{workspace_id}", headers=user_headers)
        assert response.status_code == status.HTTP_403_FORBIDDEN


class TestAdminSubscriptionManagement:
    """Test subscription update and management endpoints."""

    def test_renew_subscription_success(self, test_client, created_test_admin, sample_workspace_with_subscription):
        """Test successful subscription renewal."""
        admin_login_response = test_client.post(
            "/api/v1/auth/login",
            json={
                "email": created_test_admin.email,
                "password": "AdminPassword123!",  # pragma: allowlist secret
                "remember_me": False,
            },
        )
        admin_token = admin_login_response.json()["data"]["access_token"]
        admin_headers = {"Authorization": f"Bearer {admin_token}"}

        workspace_id = sample_workspace_with_subscription["workspace_id"]

        # Renew subscription for 3 months
        renewal_data = {"action": "renew", "extend_months": 3, "remarks": "3-month renewal for testing"}

        response = test_client.put(
            f"/api/v1/admin/workspaces/{workspace_id}/subscription", json=renewal_data, headers=admin_headers
        )

        assert response.status_code == status.HTTP_200_OK
        data = response.json()

        # Verify response structure
        assert data["success"] is True
        assert "message" in data
        assert data["action_performed"] == "renew"
        assert data["workspace_id"] == workspace_id
        assert "subscription_details" in data
        assert "payment_details" in data

    def test_upgrade_plan_success(
        self, test_client, created_test_admin, sample_workspace_with_subscription, sample_plans
    ):
        """Test successful plan upgrade."""
        admin_login_response = test_client.post(
            "/api/v1/auth/login",
            json={
                "email": created_test_admin.email,
                "password": "AdminPassword123!",  # pragma: allowlist secret
                "remember_me": False,
            },
        )
        admin_token = admin_login_response.json()["data"]["access_token"]
        admin_headers = {"Authorization": f"Bearer {admin_token}"}

        workspace_id = sample_workspace_with_subscription["workspace_id"]
        new_plan = sample_plans["active_public_plan"]

        # Upgrade plan
        upgrade_data = {
            "action": "upgrade_plan",
            "plan_id": str(new_plan.id),
            "billing_cycle": "yearly",
            "remarks": "Upgrade to yearly premium plan",
        }

        response = test_client.put(
            f"/api/v1/admin/workspaces/{workspace_id}/subscription", json=upgrade_data, headers=admin_headers
        )

        assert response.status_code == status.HTTP_200_OK
        data = response.json()

        # Verify response structure
        assert data["success"] is True
        assert data["action_performed"] == "upgrade_plan"
        assert "subscription_details" in data
        assert "payment_details" in data

    def test_trial_to_paid_conversion_success(self, test_client, created_test_admin, sample_trial_workspace):
        """Test successful trial to paid conversion."""
        admin_login_response = test_client.post(
            "/api/v1/auth/login",
            json={
                "email": created_test_admin.email,
                "password": "AdminPassword123!",  # pragma: allowlist secret
                "remember_me": False,
            },
        )
        admin_token = admin_login_response.json()["data"]["access_token"]
        admin_headers = {"Authorization": f"Bearer {admin_token}"}

        workspace_id = sample_trial_workspace["workspace_id"]

        # Convert trial to paid
        conversion_data = {
            "action": "trial_to_paid",
            "billing_cycle": "monthly",
            "remarks": "Converting trial to monthly subscription",
        }

        response = test_client.put(
            f"/api/v1/admin/workspaces/{workspace_id}/subscription", json=conversion_data, headers=admin_headers
        )

        assert response.status_code == status.HTTP_200_OK
        data = response.json()

        # Verify response structure
        assert data["success"] is True
        assert data["action_performed"] == "trial_to_paid"
        assert "subscription_details" in data
        assert data["subscription_details"]["is_trial"] is False

    def test_change_billing_cycle_success(self, test_client, created_test_admin, sample_workspace_with_subscription):
        """Test successful billing cycle change."""
        admin_login_response = test_client.post(
            "/api/v1/auth/login",
            json={
                "email": created_test_admin.email,
                "password": "AdminPassword123!",  # pragma: allowlist secret
                "remember_me": False,
            },
        )
        admin_token = admin_login_response.json()["data"]["access_token"]
        admin_headers = {"Authorization": f"Bearer {admin_token}"}

        workspace_id = sample_workspace_with_subscription["workspace_id"]

        # Change billing cycle
        billing_data = {
            "action": "change_billing",
            "billing_cycle": "yearly",
            "remarks": "Switching to yearly billing for discount",
        }

        response = test_client.put(
            f"/api/v1/admin/workspaces/{workspace_id}/subscription", json=billing_data, headers=admin_headers
        )

        assert response.status_code == status.HTTP_200_OK
        data = response.json()

        # Verify response structure
        assert data["success"] is True
        assert data["action_performed"] == "change_billing"
        assert "subscription_details" in data

    def test_cancel_subscription_success(self, test_client, created_test_admin, sample_workspace_with_subscription):
        """Test successful subscription cancellation."""
        admin_login_response = test_client.post(
            "/api/v1/auth/login",
            json={
                "email": created_test_admin.email,
                "password": "AdminPassword123!",  # pragma: allowlist secret
                "remember_me": False,
            },
        )
        admin_token = admin_login_response.json()["data"]["access_token"]
        admin_headers = {"Authorization": f"Bearer {admin_token}"}

        workspace_id = sample_workspace_with_subscription["workspace_id"]

        # Cancel subscription
        cancellation_data = {
            "cancel_immediately": False,
            "cancellation_reason": "Customer requested cancellation",
            "refund_amount": 25.00,
            "refund_reference": "REFUND_TEST_001",
        }

        response = test_client.request(
            "DELETE",
            f"/api/v1/admin/workspaces/{workspace_id}/subscription",
            json=cancellation_data,
            headers=admin_headers,
        )

        assert response.status_code == status.HTTP_200_OK
        data = response.json()

        # Verify response structure
        assert data["success"] is True
        assert data["action_performed"] == "cancelled"
        assert "subscription_details" in data
        assert data["subscription_details"]["status"] == "cancelled"

    def test_update_workspace_status_success(self, test_client, created_test_admin, sample_workspace_with_subscription):
        """Test successful workspace status update."""
        admin_login_response = test_client.post(
            "/api/v1/auth/login",
            json={
                "email": created_test_admin.email,
                "password": "AdminPassword123!",  # pragma: allowlist secret
                "remember_me": False,
            },
        )
        admin_token = admin_login_response.json()["data"]["access_token"]
        admin_headers = {"Authorization": f"Bearer {admin_token}"}

        workspace_id = sample_workspace_with_subscription["workspace_id"]

        # Update workspace status
        status_data = {"status": "archived", "reason": "Customer account suspended for testing"}

        response = test_client.patch(
            f"/api/v1/admin/workspaces/{workspace_id}/status", json=status_data, headers=admin_headers
        )

        assert response.status_code == status.HTTP_200_OK
        data = response.json()

        # Verify response structure
        assert data["success"] is True
        assert "status_changed_to_archived" in data["action_performed"]


class TestAdminWorkspaceValidationAndErrors:
    """Test validation and error handling for admin workspace management."""

    def test_subscription_update_invalid_action(
        self, test_client, created_test_admin, sample_workspace_with_subscription
    ):
        """Test subscription update with invalid action."""
        admin_login_response = test_client.post(
            "/api/v1/auth/login",
            json={
                "email": created_test_admin.email,
                "password": "AdminPassword123!",  # pragma: allowlist secret
                "remember_me": False,
            },
        )
        admin_token = admin_login_response.json()["data"]["access_token"]
        admin_headers = {"Authorization": f"Bearer {admin_token}"}

        workspace_id = sample_workspace_with_subscription["workspace_id"]

        # Invalid action
        invalid_data = {"action": "invalid_action", "remarks": "Testing invalid action"}

        response = test_client.put(
            f"/api/v1/admin/workspaces/{workspace_id}/subscription", json=invalid_data, headers=admin_headers
        )

        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    def test_subscription_update_missing_required_fields(
        self, test_client, created_test_admin, sample_workspace_with_subscription
    ):
        """Test subscription update with missing required fields."""
        admin_login_response = test_client.post(
            "/api/v1/auth/login",
            json={
                "email": created_test_admin.email,
                "password": "AdminPassword123!",  # pragma: allowlist secret
                "remember_me": False,
            },
        )
        admin_token = admin_login_response.json()["data"]["access_token"]
        admin_headers = {"Authorization": f"Bearer {admin_token}"}

        workspace_id = sample_workspace_with_subscription["workspace_id"]

        # Missing billing_cycle for trial_to_paid action
        invalid_data = {"action": "trial_to_paid", "remarks": "Missing billing cycle"}

        response = test_client.put(
            f"/api/v1/admin/workspaces/{workspace_id}/subscription", json=invalid_data, headers=admin_headers
        )

        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    def test_subscription_update_invalid_plan_id(
        self, test_client, created_test_admin, sample_workspace_with_subscription
    ):
        """Test subscription update with invalid plan ID."""
        admin_login_response = test_client.post(
            "/api/v1/auth/login",
            json={
                "email": created_test_admin.email,
                "password": "AdminPassword123!",  # pragma: allowlist secret
                "remember_me": False,
            },
        )
        admin_token = admin_login_response.json()["data"]["access_token"]
        admin_headers = {"Authorization": f"Bearer {admin_token}"}

        workspace_id = sample_workspace_with_subscription["workspace_id"]

        # Invalid plan ID
        invalid_data = {
            "action": "upgrade_plan",
            "plan_id": "00000000-0000-0000-0000-000000000000",
            "remarks": "Testing invalid plan ID",
        }

        response = test_client.put(
            f"/api/v1/admin/workspaces/{workspace_id}/subscription", json=invalid_data, headers=admin_headers
        )

        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR

    def test_subscription_update_nonexistent_workspace(self, test_client, created_test_admin):
        """Test subscription update with nonexistent workspace."""
        admin_login_response = test_client.post(
            "/api/v1/auth/login",
            json={
                "email": created_test_admin.email,
                "password": "AdminPassword123!",  # pragma: allowlist secret
                "remember_me": False,
            },
        )
        admin_token = admin_login_response.json()["data"]["access_token"]
        admin_headers = {"Authorization": f"Bearer {admin_token}"}

        # Nonexistent workspace ID
        nonexistent_id = str(uuid4())

        renewal_data = {"action": "renew", "extend_months": 1, "remarks": "Testing nonexistent workspace"}

        response = test_client.put(
            f"/api/v1/admin/workspaces/{nonexistent_id}/subscription", json=renewal_data, headers=admin_headers
        )

        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR

    def test_workspace_status_update_invalid_status(
        self, test_client, created_test_admin, sample_workspace_with_subscription
    ):
        """Test workspace status update with invalid status."""
        admin_login_response = test_client.post(
            "/api/v1/auth/login",
            json={
                "email": created_test_admin.email,
                "password": "AdminPassword123!",  # pragma: allowlist secret
                "remember_me": False,
            },
        )
        admin_token = admin_login_response.json()["data"]["access_token"]
        admin_headers = {"Authorization": f"Bearer {admin_token}"}

        workspace_id = sample_workspace_with_subscription["workspace_id"]

        # Invalid status
        invalid_data = {"status": "invalid_status", "reason": "Testing invalid status"}

        response = test_client.patch(
            f"/api/v1/admin/workspaces/{workspace_id}/status", json=invalid_data, headers=admin_headers
        )

        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    def test_regular_user_cannot_update_subscription(
        self, test_client, created_test_user, sample_workspace_with_subscription
    ):
        """Test that regular users cannot update subscriptions."""
        user_login_response = test_client.post(
            "/api/v1/auth/login",
            json={
                "email": created_test_user["email"],
                "password": created_test_user["password"],  # Use the actual password from fixture
                "remember_me": False,
            },
        )
        assert user_login_response.status_code == status.HTTP_200_OK
        user_token = user_login_response.json()["data"]["access_token"]
        user_headers = {"Authorization": f"Bearer {user_token}"}

        workspace_id = sample_workspace_with_subscription["workspace_id"]

        renewal_data = {"action": "renew", "extend_months": 1, "remarks": "Unauthorized attempt"}

        response = test_client.put(
            f"/api/v1/admin/workspaces/{workspace_id}/subscription", json=renewal_data, headers=user_headers
        )

        assert response.status_code == status.HTTP_403_FORBIDDEN

    def test_regular_user_cannot_cancel_subscription(
        self, test_client, created_test_user, sample_workspace_with_subscription
    ):
        """Test that regular users cannot cancel subscriptions."""
        user_login_response = test_client.post(
            "/api/v1/auth/login",
            json={
                "email": created_test_user["email"],
                "password": created_test_user["password"],  # Use the actual password from fixture
                "remember_me": False,
            },
        )
        assert user_login_response.status_code == status.HTTP_200_OK
        user_token = user_login_response.json()["data"]["access_token"]
        user_headers = {"Authorization": f"Bearer {user_token}"}

        workspace_id = sample_workspace_with_subscription["workspace_id"]

        cancellation_data = {"cancel_immediately": True, "cancellation_reason": "Unauthorized attempt"}

        response = test_client.request(
            "DELETE",
            f"/api/v1/admin/workspaces/{workspace_id}/subscription",
            json=cancellation_data,
            headers=user_headers,
        )

        assert response.status_code == status.HTTP_403_FORBIDDEN
