"""Tests for admin user creation workflow."""

from fastapi import status


class TestAdminUserCreationWorkflow:
    """Test the complete admin user creation workflow."""

    def test_admin_create_user_trial_subscription_success(self, test_client, created_test_admin, sample_plans):
        """Test successful admin user creation with trial subscription."""

        # Get admin token
        admin_login_response = test_client.post(
            "/api/v1/auth/login",
            json={
                "email": created_test_admin.email,
                "password": "AdminPassword123!",  # pragma: allowlist secret
                "remember_me": False,
            },
        )
        assert admin_login_response.status_code == status.HTTP_200_OK
        admin_token = admin_login_response.json()["data"]["access_token"]
        admin_headers = {"Authorization": f"Bearer {admin_token}"}

        # Get an active plan for testing
        test_plan = sample_plans["active_public_plan"]

        # Create user with trial subscription (auto-generated workspace)
        user_data = {
            "name": "<PERSON>",
            "email": "<EMAIL>",
            # workspace_name not provided - will auto-generate "john_doe_workspace"
            "plan_id": str(test_plan.id),
            "is_trial": True,  # Explicit trial choice
            "payment_remarks": "Trial subscription created by admin",
        }

        response = test_client.post("/api/v1/admin/users/create", json=user_data, headers=admin_headers)

        assert response.status_code == status.HTTP_200_OK
        data = response.json()

        # Verify response structure
        assert data["success"] is True
        assert "Successfully <NAME_EMAIL>" in data["message"]

        # Verify user details
        assert data["user_email"] == "<EMAIL>"
        assert data["user_name"] == "John Doe"
        assert data["user_status"] == "pending"

        # Verify workspace details (auto-generated)
        assert data["workspace_name"] == "john_doe_workspace"
        assert data["workspace_role"] == "owner"

        # Verify subscription details
        assert data["plan_name"] == test_plan.name
        assert data["billing_cycle"] == "trial"
        assert data["is_trial"] is True

        # Verify payment details (auto-calculated)
        assert float(data["amount_paid"]) == 0.00
        # Payment reference should be auto-generated for trials (trial_xxxxxxxx)
        assert data["payment_reference"].startswith("trial_")
        assert len(data["payment_reference"]) == 14  # "trial_" + 8 chars

        # Verify admin details
        assert data["created_by_admin"] == str(created_test_admin.id)

        # Verify all IDs are present
        assert data["user_id"]
        assert data["workspace_id"]
        assert data["subscription_id"]
        assert data["payment_id"]

    def test_admin_create_user_paid_subscription_success(self, test_client, created_test_admin, sample_plans):
        """Test successful admin user creation with paid subscription."""

        # Get admin token
        admin_login_response = test_client.post(
            "/api/v1/auth/login",
            json={
                "email": created_test_admin.email,
                "password": "AdminPassword123!",  # pragma: allowlist secret
                "remember_me": False,
            },
        )
        assert admin_login_response.status_code == status.HTTP_200_OK
        admin_token = admin_login_response.json()["data"]["access_token"]
        admin_headers = {"Authorization": f"Bearer {admin_token}"}

        # Get an active plan for testing
        test_plan = sample_plans["active_public_plan"]

        # Create user with paid monthly subscription (custom workspace name)
        user_data = {
            "name": "Jane Smith",
            "email": "<EMAIL>",
            # Custom workspace name
            "plan_id": str(test_plan.id),
            "is_trial": False,  # Paid subscription
            "billing_cycle": "monthly",
            "payment_remarks": "Monthly subscription for new client",
        }

        response = test_client.post("/api/v1/admin/users/create", json=user_data, headers=admin_headers)

        assert response.status_code == status.HTTP_200_OK
        data = response.json()

        # Verify response structure
        assert data["success"] is True
        assert "Successfully <NAME_EMAIL>" in data["message"]

        # Verify subscription details for paid plan
        assert data["billing_cycle"] == "monthly"
        assert data["is_trial"] is False
        # Amount should be auto-calculated from plan price
        assert float(data["amount_paid"]) > 0  # Should be plan's monthly price
        # Payment reference should be auto-generated (monthly_xxxxxxxx)
        assert data["payment_reference"].startswith("monthly_")
        assert len(data["payment_reference"]) == 16  # "monthly_" + 8 chars

        # Verify admin details
        assert data["created_by_admin"] == str(created_test_admin.id)

    def test_admin_create_user_yearly_subscription_success(self, test_client, created_test_admin, sample_plans):
        """Test successful admin user creation with yearly subscription."""

        # Get admin token
        admin_login_response = test_client.post(
            "/api/v1/auth/login",
            json={
                "email": created_test_admin.email,
                "password": "AdminPassword123!",  # pragma: allowlist secret
                "remember_me": False,
            },
        )
        assert admin_login_response.status_code == status.HTTP_200_OK
        admin_token = admin_login_response.json()["data"]["access_token"]
        admin_headers = {"Authorization": f"Bearer {admin_token}"}

        # Get an active plan for testing
        test_plan = sample_plans["active_public_plan"]

        # Create user with paid yearly subscription
        user_data = {
            "name": "Bob Wilson",
            "email": "<EMAIL>",
            "plan_id": str(test_plan.id),
            "is_trial": False,  # Paid subscription
            "billing_cycle": "yearly",
            "payment_remarks": "Yearly subscription for new client",
        }

        response = test_client.post("/api/v1/admin/users/create", json=user_data, headers=admin_headers)

        assert response.status_code == status.HTTP_200_OK
        data = response.json()

        # Verify response structure
        assert data["success"] is True
        assert "Successfully <NAME_EMAIL>" in data["message"]

        # Verify subscription details for yearly plan
        assert data["billing_cycle"] == "yearly"
        assert data["is_trial"] is False
        # Amount should be auto-calculated from plan price
        assert float(data["amount_paid"]) > 0  # Should be plan's yearly price
        # Payment reference should be auto-generated (yearly_xxxxxxxx)
        assert data["payment_reference"].startswith("yearly_")
        assert len(data["payment_reference"]) == 15  # "yearly_" + 8 chars

    def test_admin_create_user_duplicate_email_fails(self, test_client, created_test_admin, sample_plans):
        """Test that creating user with duplicate email fails."""

        # Get admin token
        admin_login_response = test_client.post(
            "/api/v1/auth/login",
            json={
                "email": created_test_admin.email,
                "password": "AdminPassword123!",  # pragma: allowlist secret
                "remember_me": False,
            },
        )
        assert admin_login_response.status_code == status.HTTP_200_OK
        admin_token = admin_login_response.json()["data"]["access_token"]
        admin_headers = {"Authorization": f"Bearer {admin_token}"}

        # Get an active plan for testing
        test_plan = sample_plans["active_public_plan"]

        # Create first user
        user_data = {
            "name": "John Doe",
            "email": "<EMAIL>",
            "plan_id": str(test_plan.id),
            "is_trial": True,
        }

        response1 = test_client.post("/api/v1/admin/users/create", json=user_data, headers=admin_headers)
        assert response1.status_code == status.HTTP_200_OK

        # Try to create second user with same email
        response2 = test_client.post("/api/v1/admin/users/create", json=user_data, headers=admin_headers)
        assert response2.status_code == status.HTTP_400_BAD_REQUEST
        assert "already exists" in response2.json()["detail"]

    def test_admin_create_user_invalid_plan_fails(self, test_client, created_test_admin):
        """Test that creating user with invalid plan ID fails."""

        # Get admin token
        admin_login_response = test_client.post(
            "/api/v1/auth/login",
            json={
                "email": created_test_admin.email,
                "password": "AdminPassword123!",  # pragma: allowlist secret
                "remember_me": False,
            },
        )
        assert admin_login_response.status_code == status.HTTP_200_OK
        admin_token = admin_login_response.json()["data"]["access_token"]
        admin_headers = {"Authorization": f"Bearer {admin_token}"}

        # Create user with invalid plan ID
        user_data = {
            "name": "John Doe",
            "email": "<EMAIL>",
            "plan_id": "00000000-0000-0000-0000-000000000000",  # Invalid UUID
            "is_trial": True,
        }

        response = test_client.post("/api/v1/admin/users/create", json=user_data, headers=admin_headers)
        assert response.status_code == status.HTTP_404_NOT_FOUND
        assert "not found" in response.json()["detail"]

    def test_regular_user_cannot_create_users(self, test_client, created_test_user, sample_plans):
        """Test that regular users cannot create users."""

        # Get regular user token
        user_login_response = test_client.post(
            "/api/v1/auth/login",
            json={
                "email": created_test_user.email,
                "password": "UserPassword123!",  # pragma: allowlist secret
                "remember_me": False,
            },
        )
        assert user_login_response.status_code == status.HTTP_200_OK
        user_token = user_login_response.json()["data"]["access_token"]
        user_headers = {"Authorization": f"Bearer {user_token}"}

        # Get an active plan for testing
        test_plan = sample_plans["active_public_plan"]

        # Try to create user as regular user
        user_data = {
            "name": "John Doe",
            "email": "<EMAIL>",
            "plan_id": str(test_plan.id),
            "is_trial": True,
        }

        response = test_client.post("/api/v1/admin/users/create", json=user_data, headers=user_headers)
        assert response.status_code == status.HTTP_403_FORBIDDEN

    def test_unauthenticated_user_cannot_create_users(self, test_client, sample_plans):
        """Test that unauthenticated users cannot create users."""

        # Get an active plan for testing
        test_plan = sample_plans["active_public_plan"]

        # Try to create user without authentication
        user_data = {
            "name": "John Doe",
            "email": "<EMAIL>",
            "plan_id": str(test_plan.id),
            "is_trial": True,
        }

        response = test_client.post("/api/v1/admin/users/create", json=user_data)
        assert response.status_code == status.HTTP_401_UNAUTHORIZED


class TestAdminUserCreationValidation:
    """Test validation for admin user creation."""

    def test_invalid_email_validation(self, test_client, created_test_admin, sample_plans):
        """Test validation of invalid email addresses."""

        # Get admin token
        admin_login_response = test_client.post(
            "/api/v1/auth/login",
            json={
                "email": created_test_admin.email,
                "password": "AdminPassword123!",  # pragma: allowlist secret
                "remember_me": False,
            },
        )
        assert admin_login_response.status_code == status.HTTP_200_OK
        admin_token = admin_login_response.json()["data"]["access_token"]
        admin_headers = {"Authorization": f"Bearer {admin_token}"}

        # Get an active plan for testing
        test_plan = sample_plans["active_public_plan"]

        # Test invalid email
        user_data = {
            "name": "John Doe",
            "email": "invalid-email",
            "plan_id": str(test_plan.id),
            "is_trial": True,
        }

        response = test_client.post("/api/v1/admin/users/create", json=user_data, headers=admin_headers)
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    def test_empty_name_validation(self, test_client, created_test_admin, sample_plans):
        """Test validation of empty name."""

        # Get admin token
        admin_login_response = test_client.post(
            "/api/v1/auth/login",
            json={
                "email": created_test_admin.email,
                "password": "AdminPassword123!",  # pragma: allowlist secret
                "remember_me": False,
            },
        )
        assert admin_login_response.status_code == status.HTTP_200_OK
        admin_token = admin_login_response.json()["data"]["access_token"]
        admin_headers = {"Authorization": f"Bearer {admin_token}"}

        # Get an active plan for testing
        test_plan = sample_plans["active_public_plan"]

        # Test empty name
        user_data = {
            "name": "",
            "email": "<EMAIL>",
            "plan_id": str(test_plan.id),
            "is_trial": True,
        }

        response = test_client.post("/api/v1/admin/users/create", json=user_data, headers=admin_headers)
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    def test_empty_workspace_name_auto_generates(self, test_client, created_test_admin, sample_plans):
        """Test that empty workspace name auto-generates from user name."""

        # Get admin token
        admin_login_response = test_client.post(
            "/api/v1/auth/login",
            json={
                "email": created_test_admin.email,
                "password": "AdminPassword123!",  # pragma: allowlist secret
                "remember_me": False,
            },
        )
        assert admin_login_response.status_code == status.HTTP_200_OK
        admin_token = admin_login_response.json()["data"]["access_token"]
        admin_headers = {"Authorization": f"Bearer {admin_token}"}

        # Get an active plan for testing
        test_plan = sample_plans["active_public_plan"]

        # Create user without workspace name
        user_data = {
            "name": "Alice Johnson",
            "email": "<EMAIL>",
            "plan_id": str(test_plan.id),
            "is_trial": True,
        }

        response = test_client.post("/api/v1/admin/users/create", json=user_data, headers=admin_headers)
        assert response.status_code == status.HTTP_200_OK
        data = response.json()

        # Verify workspace name was auto-generated
        assert data["workspace_name"] == "alice_johnson_workspace"

    def test_paid_subscription_requires_billing_cycle(self, test_client, created_test_admin, sample_plans):
        """Test that paid subscriptions require billing cycle."""

        # Get admin token
        admin_login_response = test_client.post(
            "/api/v1/auth/login",
            json={
                "email": created_test_admin.email,
                "password": "AdminPassword123!",  # pragma: allowlist secret
                "remember_me": False,
            },
        )
        assert admin_login_response.status_code == status.HTTP_200_OK
        admin_token = admin_login_response.json()["data"]["access_token"]
        admin_headers = {"Authorization": f"Bearer {admin_token}"}

        # Get an active plan for testing
        test_plan = sample_plans["active_public_plan"]

        # Try to create paid subscription without billing cycle
        user_data = {
            "name": "John Doe",
            "email": "<EMAIL>",
            "plan_id": str(test_plan.id),
            "is_trial": False,  # Paid subscription
            # billing_cycle missing - should fail
        }

        response = test_client.post("/api/v1/admin/users/create", json=user_data, headers=admin_headers)
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    def test_invalid_billing_cycle_validation(self, test_client, created_test_admin, sample_plans):
        """Test validation of invalid billing cycle."""

        # Get admin token
        admin_login_response = test_client.post(
            "/api/v1/auth/login",
            json={
                "email": created_test_admin.email,
                "password": "AdminPassword123!",  # pragma: allowlist secret
                "remember_me": False,
            },
        )
        assert admin_login_response.status_code == status.HTTP_200_OK
        admin_token = admin_login_response.json()["data"]["access_token"]
        admin_headers = {"Authorization": f"Bearer {admin_token}"}

        # Get an active plan for testing
        test_plan = sample_plans["active_public_plan"]

        # Test invalid billing cycle
        user_data = {
            "name": "John Doe",
            "email": "<EMAIL>",
            "plan_id": str(test_plan.id),
            "is_trial": False,
            "billing_cycle": "invalid",  # Invalid billing cycle
        }

        response = test_client.post("/api/v1/admin/users/create", json=user_data, headers=admin_headers)
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
