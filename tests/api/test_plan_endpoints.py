"""API tests for plan endpoints."""

import uuid

from fastapi import status


class TestCreatePlanEndpoint:
    """Test cases for create plan endpoint (POST /v1/plans/)."""

    def test_create_plan_success_admin(self, test_client, created_test_admin, valid_plan_data):
        """Test successful plan creation by admin."""
        # Login as admin to get token
        login_data = {
            "email": created_test_admin.email,
            "password": "AdminPassword123!",  # pragma: allowlist secret
            "remember_me": False,
        }
        login_response = test_client.post("/api/v1/auth/login", json=login_data)
        assert login_response.status_code == status.HTTP_200_OK
        token = login_response.json()["data"]["access_token"]

        # Create plan
        headers = {"Authorization": f"Bearer {token}"}
        response = test_client.post("/api/v1/plans/", json=valid_plan_data, headers=headers)

        assert response.status_code == status.HTTP_201_CREATED
        data = response.json()
        assert data["result"] == "success"
        assert "data" in data
        assert data["data"]["name"] == valid_plan_data["name"]
        assert data["data"]["plan_code"] == valid_plan_data["plan_code"]
        assert data["data"]["is_active"] == valid_plan_data["is_active"]
        assert data["data"]["is_public"] == valid_plan_data["is_public"]

    def test_create_plan_with_optional_fields(self, test_client, created_test_admin, minimal_plan_data):
        """Test plan creation with only required fields."""
        # Login as admin
        login_data = {
            "email": created_test_admin.email,
            "password": "AdminPassword123!",  # pragma: allowlist secret
            "remember_me": False,
        }
        login_response = test_client.post("/api/v1/auth/login", json=login_data)
        token = login_response.json()["data"]["access_token"]

        # Create plan with minimal data
        headers = {"Authorization": f"Bearer {token}"}
        response = test_client.post("/api/v1/plans/", json=minimal_plan_data, headers=headers)

        assert response.status_code == status.HTTP_201_CREATED
        data = response.json()
        assert data["data"]["name"] == minimal_plan_data["name"]
        assert data["data"]["plan_code"] == minimal_plan_data["plan_code"]
        assert data["data"]["description"] is None
        assert data["data"]["price_per_month"] == 9.99

    def test_create_plan_missing_required_fields(self, test_client, created_test_admin):
        """Test plan creation with missing required fields."""
        # Login as admin
        login_data = {
            "email": created_test_admin.email,
            "password": "AdminPassword123!",  # pragma: allowlist secret
            "remember_me": False,
        }
        login_response = test_client.post("/api/v1/auth/login", json=login_data)
        token = login_response.json()["data"]["access_token"]
        headers = {"Authorization": f"Bearer {token}"}

        # Missing name
        response = test_client.post(
            "/api/v1/plans/",
            json={"plan_code": "test-code"},
            headers=headers,
        )
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

        # Missing plan_code
        response = test_client.post(
            "/api/v1/plans/",
            json={"name": "Test Plan"},
            headers=headers,
        )
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

        # Empty name
        response = test_client.post(
            "/api/v1/plans/",
            json={"name": "", "plan_code": "test-code"},
            headers=headers,
        )
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    def test_create_plan_duplicate_plan_code(self, test_client, created_test_admin, valid_plan_data):
        """Test plan creation with duplicate plan_code."""
        # Login as admin
        login_data = {
            "email": created_test_admin.email,
            "password": "AdminPassword123!",  # pragma: allowlist secret
            "remember_me": False,
        }
        login_response = test_client.post("/api/v1/auth/login", json=login_data)
        token = login_response.json()["data"]["access_token"]
        headers = {"Authorization": f"Bearer {token}"}

        # Create first plan
        response = test_client.post("/api/v1/plans/", json=valid_plan_data, headers=headers)
        assert response.status_code == status.HTTP_201_CREATED

        # Try to create second plan with same plan_code
        duplicate_data = valid_plan_data.copy()
        duplicate_data["name"] = "Different Name"
        response = test_client.post("/api/v1/plans/", json=duplicate_data, headers=headers)
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert "already exists" in response.json()["detail"]

    def test_create_plan_invalid_values(self, test_client, created_test_admin):
        """Test plan creation with invalid values."""
        # Login as admin
        login_data = {
            "email": created_test_admin.email,
            "password": "AdminPassword123!",  # pragma: allowlist secret
            "remember_me": False,
        }
        login_response = test_client.post("/api/v1/auth/login", json=login_data)
        token = login_response.json()["data"]["access_token"]
        headers = {"Authorization": f"Bearer {token}"}

        # Negative price
        response = test_client.post(
            "/api/v1/plans/",
            json={
                "name": "Test Plan",
                "plan_code": "test-code",
                "price_per_month": -10.0,
                "features": {},
            },
            headers=headers,
        )
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

        # Name exceeding max length
        response = test_client.post(
            "/api/v1/plans/",
            json={
                "name": "x" * 65,  # Exceeds 64 character limit
                "plan_code": "test-code",
                "features": {},
            },
            headers=headers,
        )
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    def test_create_plan_unauthorized_regular_user(self, test_client, created_test_user, valid_plan_data):
        """Test plan creation by regular user (should fail)."""
        # Login as regular user
        login_data = {
            "email": created_test_user["email"],
            "password": created_test_user["password"],
            "remember_me": False,
        }
        login_response = test_client.post("/api/v1/auth/login", json=login_data)
        token = login_response.json()["data"]["access_token"]

        # Try to create plan
        headers = {"Authorization": f"Bearer {token}"}
        response = test_client.post("/api/v1/plans/", json=valid_plan_data, headers=headers)
        assert response.status_code == status.HTTP_403_FORBIDDEN

    def test_create_plan_unauthenticated(self, test_client, valid_plan_data):
        """Test plan creation without authentication."""
        response = test_client.post("/api/v1/plans/", json=valid_plan_data)
        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    def test_create_plan_same_name_different_code(self, test_client, created_test_admin, valid_plan_data):
        """Test creating plans with same name but different plan_code (versioning)."""
        # Login as admin
        login_data = {
            "email": created_test_admin.email,
            "password": "AdminPassword123!",  # pragma: allowlist secret
            "remember_me": False,
        }
        login_response = test_client.post("/api/v1/auth/login", json=login_data)
        token = login_response.json()["data"]["access_token"]
        headers = {"Authorization": f"Bearer {token}"}

        # Create first plan
        response = test_client.post("/api/v1/plans/", json=valid_plan_data, headers=headers)
        assert response.status_code == status.HTTP_201_CREATED

        # Create second plan with same name but different plan_code
        version_data = valid_plan_data.copy()
        version_data["plan_code"] = f"{valid_plan_data['plan_code']}-v2"
        response = test_client.post("/api/v1/plans/", json=version_data, headers=headers)
        assert response.status_code == status.HTTP_201_CREATED

    def test_create_plan_trial_validation(self, test_client, created_test_admin):
        """Test plan creation with trial validation."""
        # Login as admin
        login_data = {
            "email": created_test_admin.email,
            "password": "AdminPassword123!",  # pragma: allowlist secret
            "remember_me": False,
        }
        login_response = test_client.post("/api/v1/auth/login", json=login_data)
        token = login_response.json()["data"]["access_token"]
        headers = {"Authorization": f"Bearer {token}"}

        # Trial available but no duration
        response = test_client.post(
            "/api/v1/plans/",
            json={
                "name": "Trial Plan",
                "plan_code": "TRIAL_PLAN",
                "price_per_month": 19.99,
                "trial_available": True,
                "features": {},
            },
            headers=headers,
        )
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

        # Trial not available but duration provided
        response = test_client.post(
            "/api/v1/plans/",
            json={
                "name": "No Trial Plan",
                "plan_code": "NO_TRIAL_PLAN",
                "price_per_month": 29.99,
                "trial_available": False,
                "trial_duration_days": 30,
                "features": {},
            },
            headers=headers,
        )
        assert response.status_code == status.HTTP_400_BAD_REQUEST


class TestListPlansEndpoint:
    """Test cases for list plans endpoint (GET /v1/plans/)."""

    def test_list_plans_admin_sees_all(self, test_client, created_test_admin, sample_plans):
        """Test that admin sees all plans regardless of status."""
        # Login as admin
        login_data = {
            "email": created_test_admin.email,
            "password": "AdminPassword123!",  # pragma: allowlist secret
            "remember_me": False,
        }
        login_response = test_client.post("/api/v1/auth/login", json=login_data)
        token = login_response.json()["data"]["access_token"]

        # List plans
        headers = {"Authorization": f"Bearer {token}"}
        response = test_client.get("/api/v1/plans/", headers=headers)

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert len(data["plans"]) == 4  # All plans including inactive/private

    def test_list_plans_user_sees_filtered(self, test_client, created_test_user, sample_plans):
        """Test that regular user sees only active and public plans."""
        # Login as regular user
        login_data = {
            "email": created_test_user["email"],
            "password": created_test_user["password"],
            "remember_me": False,
        }
        login_response = test_client.post("/api/v1/auth/login", json=login_data)
        token = login_response.json()["data"]["access_token"]

        # List plans
        headers = {"Authorization": f"Bearer {token}"}
        response = test_client.get("/api/v1/plans/", headers=headers)

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert len(data["plans"]) == 1  # Only active and public plans
        assert all(plan["is_active"] and plan["is_public"] for plan in data["plans"])

    def test_list_plans_pagination(self, test_client, created_test_admin, sample_plans):
        """Test pagination in list plans."""
        # Login as admin
        login_data = {
            "email": created_test_admin.email,
            "password": "AdminPassword123!",  # pragma: allowlist secret
            "remember_me": False,
        }
        login_response = test_client.post("/api/v1/auth/login", json=login_data)
        token = login_response.json()["data"]["access_token"]

        # Test pagination
        headers = {"Authorization": f"Bearer {token}"}
        response = test_client.get("/api/v1/plans/?page=1&page_size=2", headers=headers)

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["page"] == 1
        assert data["page_size"] == 2
        assert len(data["plans"]) <= 2
        assert data["total"] == 4

    def test_list_plans_unauthenticated(self, test_client):
        """Test list plans without authentication."""
        response = test_client.get("/api/v1/plans/")
        assert response.status_code == status.HTTP_401_UNAUTHORIZED


class TestGetPlanEndpoint:
    """Test cases for get plan endpoint (GET /v1/plans/{id})."""

    def test_get_plan_by_id_admin(self, test_client, created_test_admin, sample_plans):
        """Test admin can retrieve any plan by ID."""
        # Login as admin
        login_data = {
            "email": created_test_admin.email,
            "password": "AdminPassword123!",  # pragma: allowlist secret
            "remember_me": False,
        }
        login_response = test_client.post("/api/v1/auth/login", json=login_data)
        token = login_response.json()["data"]["access_token"]

        # Get inactive plan (admin should see it)
        headers = {"Authorization": f"Bearer {token}"}
        plan_id = sample_plans["inactive_plan"].id
        response = test_client.get(f"/api/v1/plans/{plan_id}", headers=headers)

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["id"] == str(plan_id)
        assert not data["is_active"]

    def test_get_plan_by_id_user_active_public(self, test_client, created_test_user, sample_plans):
        """Test regular user can retrieve active and public plan."""
        # Login as regular user
        login_data = {
            "email": created_test_user["email"],
            "password": created_test_user["password"],
            "remember_me": False,
        }
        login_response = test_client.post("/api/v1/auth/login", json=login_data)
        token = login_response.json()["data"]["access_token"]

        # Get active and public plan
        headers = {"Authorization": f"Bearer {token}"}
        plan_id = sample_plans["active_public_plan"].id
        response = test_client.get(f"/api/v1/plans/{plan_id}", headers=headers)

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["id"] == str(plan_id)
        assert data["is_active"]
        assert data["is_public"]

    def test_get_plan_by_id_user_inactive_forbidden(self, test_client, created_test_user, sample_plans):
        """Test regular user cannot retrieve inactive plan."""
        # Login as regular user
        login_data = {
            "email": created_test_user["email"],
            "password": created_test_user["password"],
            "remember_me": False,
        }
        login_response = test_client.post("/api/v1/auth/login", json=login_data)
        token = login_response.json()["data"]["access_token"]

        # Try to get inactive plan
        headers = {"Authorization": f"Bearer {token}"}
        plan_id = sample_plans["inactive_plan"].id
        response = test_client.get(f"/api/v1/plans/{plan_id}", headers=headers)

        assert response.status_code == status.HTTP_404_NOT_FOUND

    def test_get_plan_by_id_user_private_forbidden(self, test_client, created_test_user, sample_plans):
        """Test regular user cannot retrieve private plan."""
        # Login as regular user
        login_data = {
            "email": created_test_user["email"],
            "password": created_test_user["password"],
            "remember_me": False,
        }
        login_response = test_client.post("/api/v1/auth/login", json=login_data)
        token = login_response.json()["data"]["access_token"]

        # Try to get private plan
        headers = {"Authorization": f"Bearer {token}"}
        plan_id = sample_plans["private_plan"].id
        response = test_client.get(f"/api/v1/plans/{plan_id}", headers=headers)

        assert response.status_code == status.HTTP_404_NOT_FOUND

    def test_get_plan_by_id_invalid_uuid(self, test_client, created_test_user):
        """Test get plan with invalid UUID format."""
        # Login as regular user
        login_data = {
            "email": created_test_user["email"],
            "password": created_test_user["password"],
            "remember_me": False,
        }
        login_response = test_client.post("/api/v1/auth/login", json=login_data)
        token = login_response.json()["data"]["access_token"]

        # Try to get plan with invalid UUID
        headers = {"Authorization": f"Bearer {token}"}
        response = test_client.get("/api/v1/plans/invalid-uuid", headers=headers)

        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    def test_get_plan_by_id_nonexistent(self, test_client, created_test_user):
        """Test get plan with non-existent ID."""
        # Login as regular user
        login_data = {
            "email": created_test_user["email"],
            "password": created_test_user["password"],
            "remember_me": False,
        }
        login_response = test_client.post("/api/v1/auth/login", json=login_data)
        token = login_response.json()["data"]["access_token"]

        # Try to get non-existent plan
        headers = {"Authorization": f"Bearer {token}"}
        fake_id = str(uuid.uuid4())
        response = test_client.get(f"/api/v1/plans/{fake_id}", headers=headers)

        assert response.status_code == status.HTTP_404_NOT_FOUND

    def test_get_plan_by_id_unauthenticated(self, test_client, sample_plans):
        """Test get plan without authentication."""
        plan_id = sample_plans["active_public_plan"].id
        response = test_client.get(f"/api/v1/plans/{plan_id}")
        assert response.status_code == status.HTTP_401_UNAUTHORIZED


class TestGetPlanByCodeEndpoint:
    """Test cases for get plan by code endpoint (GET /v1/plans/code/{code})."""

    def test_get_plan_by_code_admin(self, test_client, created_test_admin, sample_plans):
        """Test admin can retrieve any plan by code."""
        # Login as admin
        login_data = {
            "email": created_test_admin.email,
            "password": "AdminPassword123!",  # pragma: allowlist secret
            "remember_me": False,
        }
        login_response = test_client.post("/api/v1/auth/login", json=login_data)
        token = login_response.json()["data"]["access_token"]

        # Get inactive plan by code (admin should see it)
        headers = {"Authorization": f"Bearer {token}"}
        plan_code = sample_plans["inactive_plan"].plan_code
        response = test_client.get(f"/api/v1/plans/code/{plan_code}", headers=headers)

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["plan_code"] == plan_code
        assert not data["is_active"]

    def test_get_plan_by_code_user_filtered(self, test_client, created_test_user, sample_plans):
        """Test regular user can only retrieve active and public plan by code."""
        # Login as regular user
        login_data = {
            "email": created_test_user["email"],
            "password": created_test_user["password"],
            "remember_me": False,
        }
        login_response = test_client.post("/api/v1/auth/login", json=login_data)
        token = login_response.json()["data"]["access_token"]

        # Get active and public plan by code
        headers = {"Authorization": f"Bearer {token}"}
        plan_code = sample_plans["active_public_plan"].plan_code
        response = test_client.get(f"/api/v1/plans/code/{plan_code}", headers=headers)

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["plan_code"] == plan_code

        # Try to get inactive plan by code (should fail)
        plan_code = sample_plans["inactive_plan"].plan_code
        response = test_client.get(f"/api/v1/plans/code/{plan_code}", headers=headers)
        assert response.status_code == status.HTTP_404_NOT_FOUND

    def test_get_plan_by_code_nonexistent(self, test_client, created_test_user):
        """Test get plan with non-existent code."""
        # Login as regular user
        login_data = {
            "email": created_test_user["email"],
            "password": created_test_user["password"],
            "remember_me": False,
        }
        login_response = test_client.post("/api/v1/auth/login", json=login_data)
        token = login_response.json()["data"]["access_token"]

        # Try to get non-existent plan
        headers = {"Authorization": f"Bearer {token}"}
        response = test_client.get("/api/v1/plans/code/nonexistent-code", headers=headers)

        assert response.status_code == status.HTTP_404_NOT_FOUND

    def test_plan_code_case_insensitive_search(self, test_client, created_test_admin):
        """Test that plan code search works with different case variations."""

        # Get admin headers
        admin_login_response = test_client.post(
            "/api/v1/auth/login",
            json={
                "email": created_test_admin.email,
                "password": "AdminPassword123!",  # pragma: allowlist secret
                "remember_me": False,
            },
        )
        assert admin_login_response.status_code == status.HTTP_200_OK
        admin_token = admin_login_response.json()["data"]["access_token"]
        admin_headers = {"Authorization": f"Bearer {admin_token}"}

        # Create a plan with a specific code
        plan_data = {
            "name": "Case Sensitivity Test Plan",
            "plan_code": "test-case-plan",  # Will be converted to TEST-CASE-PLAN
            "price_per_month": 19.99,
            "description": "Testing case sensitivity",
            "features": {"test": True},
        }

        create_response = test_client.post("/api/v1/plans/", json=plan_data, headers=admin_headers)
        assert create_response.status_code == status.HTTP_201_CREATED
        created_plan = create_response.json()["data"]

        # Verify plan code was normalized to uppercase
        assert created_plan["plan_code"] == "TEST-CASE-PLAN"

        # Test different case variations for search
        test_variations = [
            "test-case-plan",  # Original lowercase
            "TEST-CASE-PLAN",  # All uppercase
            "Test-Case-Plan",  # Title case
        ]

        for variation in test_variations:
            # Search for plan using this variation
            search_response = test_client.get(f"/api/v1/plans/code/{variation}", headers=admin_headers)

            # Should find the plan regardless of case
            assert search_response.status_code == status.HTTP_200_OK, (
                f"Failed to find plan with variation '{variation}'"
            )

            found_plan = search_response.json()
            assert found_plan["id"] == created_plan["id"], f"Found different plan for variation '{variation}'"
            assert found_plan["plan_code"] == "TEST-CASE-PLAN", f"Plan code mismatch for variation '{variation}'"
