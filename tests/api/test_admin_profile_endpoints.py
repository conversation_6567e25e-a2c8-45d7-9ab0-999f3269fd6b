"""Tests for enhanced admin profile management endpoints."""

from uuid import uuid4

from fastapi import status


class TestEnhancedProfileUpdateEndpoint:
    """Test enhanced profile update functionality for both users and admins."""

    def test_user_update_own_profile_success(self, test_client, created_test_user):
        """Test regular user updating their own profile."""

        # Get user token
        user_login_response = test_client.post(
            "/api/v1/auth/login",
            json={"email": created_test_user["email"], "password": created_test_user["password"], "remember_me": False},
        )
        assert user_login_response.status_code == status.HTTP_200_OK
        user_token = user_login_response.json()["data"]["access_token"]
        user_headers = {"Authorization": f"Bearer {user_token}"}

        # Update own profile (standard fields only)
        update_data = {
            "name": "Updated User Name",
            "timezone": "America/New_York",
            "avatar": "https://example.com/new-avatar.jpg",
        }

        response = test_client.patch("/api/v1/profile/update", json=update_data, headers=user_headers)

        assert response.status_code == status.HTTP_200_OK
        data = response.json()

        assert data["success"] is True
        assert "Profile updated successfully" in data["message"]
        assert data["updated_by_admin"] is False
        assert data["user"]["name"] == "Updated User Name"
        assert data["user"]["timezone"] == "America/New_York"
        assert data["user"]["avatar"] == "https://example.com/new-avatar.jpg"
        assert data["admin_fields_updated"] == []

    def test_admin_update_other_user_profile_success(self, test_client, created_test_admin, created_test_user):
        """Test admin updating another user's profile."""

        # Get admin token
        admin_login_response = test_client.post(
            "/api/v1/auth/login",
            json={
                "email": created_test_admin.email,
                "password": "AdminPassword123!",  # pragma: allowlist secret
                "remember_me": False,
            },
        )
        assert admin_login_response.status_code == status.HTTP_200_OK
        admin_token = admin_login_response.json()["data"]["access_token"]
        admin_headers = {"Authorization": f"Bearer {admin_token}"}

        # Update user profile as admin using user_id parameter
        update_data = {
            "name": "Updated by Admin",
            "timezone": "America/New_York",
            "avatar": "https://example.com/new-avatar.jpg",
        }

        response = test_client.patch(
            f"/api/v1/profile/update?user_id={created_test_user['id']}", json=update_data, headers=admin_headers
        )

        assert response.status_code == status.HTTP_200_OK
        data = response.json()

        assert data["success"] is True
        assert "Profile updated successfully by admin" in data["message"]
        assert data["updated_by_admin"] is True
        assert data["user"]["name"] == "Updated by Admin"
        assert data["user"]["timezone"] == "America/New_York"
        assert data["user"]["avatar"] == "https://example.com/new-avatar.jpg"
        assert data["admin_fields_updated"] == []  # No admin-only fields updated

    def test_admin_update_admin_fields_success(self, test_client, created_test_admin, created_test_user):
        """Test admin updating admin-only fields."""

        # Get admin token
        admin_login_response = test_client.post(
            "/api/v1/auth/login",
            json={
                "email": created_test_admin.email,
                "password": "AdminPassword123!",  # pragma: allowlist secret
                "remember_me": False,
            },
        )
        assert admin_login_response.status_code == status.HTTP_200_OK
        admin_token = admin_login_response.json()["data"]["access_token"]
        admin_headers = {"Authorization": f"Bearer {admin_token}"}

        # Update admin-only fields using user_id parameter
        update_data = {"email": "<EMAIL>", "status": "banned", "is_admin": True}

        response = test_client.patch(
            f"/api/v1/profile/update?user_id={created_test_user['id']}", json=update_data, headers=admin_headers
        )

        assert response.status_code == status.HTTP_200_OK
        data = response.json()

        assert data["success"] is True
        assert data["updated_by_admin"] is True
        assert data["user"]["email"] == "<EMAIL>"
        assert data["user"]["status"] == "banned"
        assert data["user"]["is_admin"] is True
        assert set(data["admin_fields_updated"]) == {"email", "status", "is_admin"}

    def test_user_update_admin_fields_forbidden(self, test_client, created_test_user):
        """Test regular user cannot update admin-only fields."""

        # Get user token
        user_login_response = test_client.post(
            "/api/v1/auth/login",
            json={"email": created_test_user["email"], "password": created_test_user["password"], "remember_me": False},
        )
        assert user_login_response.status_code == status.HTTP_200_OK
        user_token = user_login_response.json()["data"]["access_token"]
        user_headers = {"Authorization": f"Bearer {user_token}"}

        # Try to update admin-only fields as regular user
        update_data = {
            "name": "Regular Update",
            "email": "<EMAIL>",  # Admin-only field
            "is_admin": True,  # Admin-only field
        }

        response = test_client.patch("/api/v1/profile/update", json=update_data, headers=user_headers)

        assert response.status_code == status.HTTP_403_FORBIDDEN
        assert "Admin privileges required to update fields" in response.json()["detail"]

    def test_admin_update_user_not_found(self, test_client, created_test_admin):
        """Test admin update with non-existent user."""

        # Get admin token
        admin_login_response = test_client.post(
            "/api/v1/auth/login",
            json={
                "email": created_test_admin.email,
                "password": "AdminPassword123!",  # pragma: allowlist secret
                "remember_me": False,
            },
        )
        assert admin_login_response.status_code == status.HTTP_200_OK
        admin_token = admin_login_response.json()["data"]["access_token"]
        admin_headers = {"Authorization": f"Bearer {admin_token}"}

        # Try to update non-existent user
        fake_user_id = str(uuid4())
        update_data = {"name": "New Name"}

        response = test_client.patch(
            f"/api/v1/profile/update?user_id={fake_user_id}", json=update_data, headers=admin_headers
        )

        assert response.status_code == status.HTTP_404_NOT_FOUND
        assert response.json()["detail"] == "User not found"

    def test_admin_update_duplicate_email(self, test_client, created_test_admin, created_test_user):
        """Test admin cannot update to existing email."""

        # Create another user
        register_response = test_client.post(
            "/api/v1/auth/register",
            json={
                "name": "Another User",
                "email": "<EMAIL>",
                "password": "AnotherPassword123!",  # pragma: allowlist secret
            },
        )
        assert register_response.status_code == status.HTTP_200_OK

        # Get admin token
        admin_login_response = test_client.post(
            "/api/v1/auth/login",
            json={
                "email": created_test_admin.email,
                "password": "AdminPassword123!",  # pragma: allowlist secret
                "remember_me": False,
            },
        )
        assert admin_login_response.status_code == status.HTTP_200_OK
        admin_token = admin_login_response.json()["data"]["access_token"]
        admin_headers = {"Authorization": f"Bearer {admin_token}"}

        # Try to update user with existing email
        update_data = {"email": "<EMAIL>"}

        response = test_client.patch(
            f"/api/v1/profile/update?user_id={created_test_user['id']}", json=update_data, headers=admin_headers
        )

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.json()["detail"] == "Email address is already in use"

    def test_user_update_other_user_forbidden(self, test_client, created_test_user):
        """Test regular user cannot update other users' profiles."""

        # Create another user
        register_response = test_client.post(
            "/api/v1/auth/register",
            json={
                "name": "Another User",
                "email": "<EMAIL>",
                "password": "AnotherPassword123!",  # pragma: allowlist secret
            },
        )
        assert register_response.status_code == status.HTTP_200_OK

        # Get the user ID by logging in and getting profile
        another_login_response = test_client.post(
            "/api/v1/auth/login",
            json={
                "email": "<EMAIL>",
                "password": "AnotherPassword123!",  # pragma: allowlist secret
                "remember_me": False,
            },
        )
        assert another_login_response.status_code == status.HTTP_200_OK
        another_token = another_login_response.json()["data"]["access_token"]

        # Get user profile to extract user ID
        profile_response = test_client.get("/api/v1/profile/me", headers={"Authorization": f"Bearer {another_token}"})
        assert profile_response.status_code == status.HTTP_200_OK
        another_user_id = profile_response.json()["id"]

        # Get regular user token
        user_login_response = test_client.post(
            "/api/v1/auth/login",
            json={"email": created_test_user["email"], "password": created_test_user["password"], "remember_me": False},
        )
        assert user_login_response.status_code == status.HTTP_200_OK
        user_token = user_login_response.json()["data"]["access_token"]
        user_headers = {"Authorization": f"Bearer {user_token}"}

        # Try to update another user's profile
        update_data = {"name": "Hacker Name"}

        response = test_client.patch(
            f"/api/v1/profile/update?user_id={another_user_id}", json=update_data, headers=user_headers
        )

        assert response.status_code == status.HTTP_403_FORBIDDEN
        assert response.json()["detail"] == "Admin privileges required to update other users"

    def test_update_unauthenticated(self, test_client, created_test_user):
        """Test unauthenticated request fails."""

        update_data = {"name": "Hacker Name"}

        response = test_client.patch("/api/v1/profile/update", json=update_data)

        assert response.status_code == status.HTTP_401_UNAUTHORIZED
