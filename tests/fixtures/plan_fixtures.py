"""Plan-related test fixtures."""

import uuid

import pytest

from src.entities.plan import Plan


@pytest.fixture
def valid_plan_data():
    """Valid plan data for testing."""
    unique_id = str(uuid.uuid4())[:8]
    return {
        "name": f"Test Plan {unique_id}",
        "plan_code": f"TEST_PLAN_{unique_id.upper()}",
        "description": "A test subscription plan",
        "price_per_month": 19.99,
        "price_per_year": 199.99,
        "is_active": True,
        "is_public": True,
        "trial_available": True,
        "trial_duration_days": 30,
        "features": {
            "max_users": 10,
            "storage_gb": 100,
            "api_calls_per_month": 10000,
            "support": "email",
        },
    }


@pytest.fixture
def minimal_plan_data():
    """Minimal plan data with only required fields."""
    unique_id = str(uuid.uuid4())[:8]
    return {
        "name": f"Minimal Plan {unique_id}",
        "plan_code": f"MINIMAL_PLAN_{unique_id.upper()}",
        "price_per_month": 9.99,  # Add required pricing
        "features": {"basic": "true"},
    }


@pytest.fixture
def premium_plan_data():
    """Premium plan data for testing."""
    unique_id = str(uuid.uuid4())[:8]
    return {
        "name": f"Premium Plan {unique_id}",
        "plan_code": f"PREMIUM_PLAN_{unique_id.upper()}",
        "description": "Premium subscription with all features",
        "price_per_month": 99.99,
        "price_per_year": 999.99,
        "is_active": True,
        "is_public": True,
        "trial_available": False,
        "features": {
            "max_users": "unlimited",
            "storage_gb": "unlimited",
            "api_calls_per_month": "unlimited",
            "support": "priority",
            "advanced_analytics": True,
            "custom_integrations": True,
        },
    }


@pytest.fixture
def free_plan_data():
    """Free plan data for testing."""
    unique_id = str(uuid.uuid4())[:8]
    return {
        "name": f"Free Plan {unique_id}",
        "plan_code": f"FREE_PLAN_{unique_id.upper()}",
        "description": "Free tier with basic features",
        "price_per_month": 0.0,
        "price_per_year": 0.0,
        "is_active": True,
        "is_public": True,
        "trial_available": False,
        "features": {
            "max_users": 1,
            "storage_gb": 1,
            "api_calls_per_month": 1000,
            "support": "community",
        },
    }


@pytest.fixture
def inactive_plan_data():
    """Inactive plan data for testing."""
    unique_id = str(uuid.uuid4())[:8]
    return {
        "name": f"Inactive Plan {unique_id}",
        "plan_code": f"INACTIVE_PLAN_{unique_id.upper()}",
        "description": "An inactive plan for testing",
        "price_per_month": 29.99,
        "is_active": False,  # Inactive
        "is_public": True,
        "trial_available": False,
        "features": {"legacy": "true"},
    }


@pytest.fixture
def private_plan_data():
    """Private plan data for testing."""
    unique_id = str(uuid.uuid4())[:8]
    return {
        "name": f"Private Plan {unique_id}",
        "plan_code": f"PRIVATE_PLAN_{unique_id.upper()}",
        "description": "A private plan for internal use",
        "price_per_month": 49.99,
        "is_active": True,
        "is_public": False,  # Private
        "trial_available": False,
        "features": {"internal": "true"},
    }


@pytest.fixture
def enterprise_plan_data():
    """Enterprise plan data for testing."""
    unique_id = str(uuid.uuid4())[:8]
    return {
        "name": f"Enterprise Plan {unique_id}",
        "plan_code": f"ENTERPRISE_PLAN_{unique_id.upper()}",
        "description": "Enterprise plan with custom pricing",
        "price_per_month": None,  # Custom pricing
        "price_per_year": None,
        "is_active": True,
        "is_public": False,  # Private enterprise plan
        "trial_available": True,
        "trial_duration_days": 14,
        "features": {
            "max_users": "unlimited",
            "storage_gb": "unlimited",
            "api_calls_per_month": "unlimited",
            "support": "dedicated",
            "sla": "99.9%",
            "custom_features": True,
            "on_premise": True,
        },
    }


@pytest.fixture
def sample_plans(test_db_session, created_test_admin):
    """Create sample plans in the database for testing."""
    plans = {}

    # Active and public plan
    active_public_plan = Plan(
        name="Active Public Plan",
        plan_code="ACTIVE_PUBLIC_PLAN",
        description="An active and public plan",
        price_per_month=19.99,
        price_per_year=199.99,
        is_active=True,
        is_public=True,
        trial_available=True,
        trial_duration_days=30,
        features={"max_users": 10, "storage_gb": 100},
        created_by=created_test_admin.id,
    )

    # Inactive plan
    inactive_plan = Plan(
        name="Inactive Plan",
        plan_code="INACTIVE_PLAN",
        description="An inactive plan",
        price_per_month=29.99,
        is_active=False,
        is_public=True,
        trial_available=False,
        features={"legacy": True},
        created_by=created_test_admin.id,
    )

    # Private plan
    private_plan = Plan(
        name="Private Plan",
        plan_code="PRIVATE_PLAN",
        description="A private plan",
        price_per_month=49.99,
        is_active=True,
        is_public=False,
        trial_available=False,
        features={"internal": True},
        created_by=created_test_admin.id,
    )

    # Inactive and private plan
    inactive_private_plan = Plan(
        name="Inactive Private Plan",
        plan_code="INACTIVE_PRIVATE_PLAN",
        description="An inactive and private plan",
        price_per_month=39.99,
        is_active=False,
        is_public=False,
        trial_available=False,
        features={"deprecated": True},
        created_by=created_test_admin.id,
    )

    # Add all plans to database
    test_db_session.add(active_public_plan)
    test_db_session.add(inactive_plan)
    test_db_session.add(private_plan)
    test_db_session.add(inactive_private_plan)
    test_db_session.commit()

    # Refresh to get IDs
    test_db_session.refresh(active_public_plan)
    test_db_session.refresh(inactive_plan)
    test_db_session.refresh(private_plan)
    test_db_session.refresh(inactive_private_plan)

    plans["active_public_plan"] = active_public_plan
    plans["inactive_plan"] = inactive_plan
    plans["private_plan"] = private_plan
    plans["inactive_private_plan"] = inactive_private_plan

    return plans


@pytest.fixture
def plan_factory(test_db_session, created_test_admin):
    """Factory to create plans with custom data."""

    def _create_plan(**kwargs):
        unique_id = str(uuid.uuid4())[:8]
        default_data = {
            "name": f"Factory Plan {unique_id}",
            "plan_code": f"FACTORY_PLAN_{unique_id.upper()}",
            "description": "A plan created by factory",
            "price_per_month": 19.99,
            "is_active": True,
            "is_public": True,
            "trial_available": False,
            "features": {"factory": True},
            "created_by": created_test_admin.id,
        }
        default_data.update(kwargs)

        plan = Plan(**default_data)
        test_db_session.add(plan)
        test_db_session.commit()
        test_db_session.refresh(plan)
        return plan

    return _create_plan


@pytest.fixture
def bulk_plans(test_db_session, created_test_admin):
    """Create multiple plans for pagination and bulk testing."""
    plans = []
    for i in range(25):  # Create 25 plans for pagination testing
        plan = Plan(
            name=f"Bulk Plan {i + 1:02d}",
            plan_code=f"BULK_PLAN_{i + 1:02d}",
            description=f"Bulk plan number {i + 1}",
            price_per_month=10.0 + i,
            is_active=i % 2 == 0,  # Alternate active/inactive
            is_public=i % 3 != 0,  # Most are public, some private
            trial_available=i % 4 == 0,  # Some have trials
            trial_duration_days=30 if i % 4 == 0 else None,
            features={"bulk_index": i, "tier": "bulk"},
            created_by=created_test_admin.id,
        )
        plans.append(plan)

    test_db_session.add_all(plans)
    test_db_session.commit()

    # Refresh all plans
    for plan in plans:
        test_db_session.refresh(plan)

    return plans


@pytest.fixture
def unicode_plan_data():
    """Plan data with unicode characters for testing."""
    unique_id = str(uuid.uuid4())[:8]
    return {
        "name": f"Unicode Plan 🚀 {unique_id}",
        "plan_code": f"UNICODE_PLAN_{unique_id.upper()}",
        "description": "Plan with émojis and spëcial characters: 中文, العربية, русский",
        "price_per_month": 25.99,
        "is_active": True,
        "is_public": True,
        "trial_available": False,
        "features": {
            "unicode_support": "✅",
            "languages": ["English", "中文", "العربية", "русский"],
            "emoji": "🎉",
        },
    }


@pytest.fixture
def complex_features_plan_data():
    """Plan data with complex nested features for testing."""
    unique_id = str(uuid.uuid4())[:8]
    return {
        "name": f"Complex Features Plan {unique_id}",
        "plan_code": f"COMPLEX_PLAN_{unique_id.upper()}",
        "description": "Plan with complex nested features",
        "price_per_month": 79.99,
        "is_active": True,
        "is_public": True,
        "trial_available": True,
        "trial_duration_days": 14,
        "features": {
            "api": {
                "rate_limit": {"requests_per_minute": 1000, "burst": 100},
                "endpoints": ["v1", "v2", "beta"],
                "authentication": ["api_key", "oauth2", "jwt"],
            },
            "storage": {
                "total_gb": 500,
                "file_types": ["images", "documents", "videos"],
                "backup": {"frequency": "daily", "retention_days": 30},
            },
            "integrations": {
                "webhooks": {"max_endpoints": 10, "retry_attempts": 3},
                "third_party": ["slack", "teams", "discord"],
            },
            "analytics": {
                "real_time": True,
                "historical_data": "1_year",
                "custom_dashboards": 5,
            },
        },
    }
