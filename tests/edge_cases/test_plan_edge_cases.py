"""Edge case tests for plan functionality."""

from fastapi import status


class TestPlanEdgeCases:
    """Test edge cases for plan functionality."""

    def test_create_plan_max_length_fields(self, test_client, created_test_admin):
        """Test creating plan with maximum length fields."""
        # Login as admin
        login_data = {
            "email": created_test_admin.email,
            "password": "AdminPassword123!",  # pragma: allowlist secret
            "remember_me": False,
        }
        login_response = test_client.post("/api/v1/auth/login", json=login_data)
        token = login_response.json()["data"]["access_token"]
        headers = {"Authorization": f"Bearer {token}"}

        # Create plan with max length fields
        plan_data = {
            "name": "x" * 64,  # Max length for name
            "plan_code": "Y" * 64,  # Max length for plan_code (uppercase)
            "price_per_month": 19.99,  # Required pricing
            "description": "z" * 255,  # Max length for description
            "features": {"test": "value"},
        }
        response = test_client.post("/api/v1/plans/", json=plan_data, headers=headers)

        assert response.status_code == status.HTTP_201_CREATED
        data = response.json()
        assert len(data["data"]["name"]) == 64
        assert len(data["data"]["plan_code"]) == 64
        assert len(data["data"]["description"]) == 255

    def test_create_plan_unicode_characters(self, test_client, created_test_admin, unicode_plan_data):
        """Test creating plan with unicode characters and emojis."""
        # Login as admin
        login_data = {
            "email": created_test_admin.email,
            "password": "AdminPassword123!",  # pragma: allowlist secret
            "remember_me": False,
        }
        login_response = test_client.post("/api/v1/auth/login", json=login_data)
        token = login_response.json()["data"]["access_token"]
        headers = {"Authorization": f"Bearer {token}"}

        # Create plan with unicode data
        response = test_client.post("/api/v1/plans/", json=unicode_plan_data, headers=headers)

        assert response.status_code == status.HTTP_201_CREATED
        data = response.json()
        assert "🚀" in data["data"]["name"]
        assert "émojis" in data["data"]["description"]
        assert data["data"]["features"]["unicode_support"] == "✅"

    def test_create_plan_complex_nested_features(self, test_client, created_test_admin, complex_features_plan_data):
        """Test creating plan with complex nested features structure."""
        # Login as admin
        login_data = {
            "email": created_test_admin.email,
            "password": "AdminPassword123!",  # pragma: allowlist secret
            "remember_me": False,
        }
        login_response = test_client.post("/api/v1/auth/login", json=login_data)
        token = login_response.json()["data"]["access_token"]
        headers = {"Authorization": f"Bearer {token}"}

        # Create plan with complex features
        response = test_client.post("/api/v1/plans/", json=complex_features_plan_data, headers=headers)

        assert response.status_code == status.HTTP_201_CREATED
        data = response.json()
        features = data["data"]["features"]
        assert "api" in features
        assert "rate_limit" in features["api"]
        assert features["api"]["rate_limit"]["requests_per_minute"] == 1000
        assert features["analytics"]["real_time"] is True

    def test_list_plans_empty_database(self, test_client, created_test_user):
        """Test listing plans when no plans exist."""
        # Login as regular user
        login_data = {
            "email": created_test_user["email"],
            "password": created_test_user["password"],
            "remember_me": False,
        }
        login_response = test_client.post("/api/v1/auth/login", json=login_data)
        token = login_response.json()["data"]["access_token"]

        # List plans from empty database
        headers = {"Authorization": f"Bearer {token}"}
        response = test_client.get("/api/v1/plans/", headers=headers)

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["plans"] == []
        assert data["total"] == 0
        assert data["page"] == 1
        assert data["total_pages"] == 0

    def test_get_plan_after_deactivation(self, test_client, created_test_admin, created_test_user, sample_plans):
        """Test retrieving plan after it has been deactivated."""
        # Login as admin
        admin_login_data = {
            "email": created_test_admin.email,
            "password": "AdminPassword123!",  # pragma: allowlist secret
            "remember_me": False,
        }
        admin_login_response = test_client.post("/api/v1/auth/login", json=admin_login_data)
        admin_token = admin_login_response.json()["data"]["access_token"]
        admin_headers = {"Authorization": f"Bearer {admin_token}"}

        # Login as regular user
        user_login_data = {
            "email": created_test_user["email"],
            "password": created_test_user["password"],
            "remember_me": False,
        }
        user_login_response = test_client.post("/api/v1/auth/login", json=user_login_data)
        user_token = user_login_response.json()["data"]["access_token"]
        user_headers = {"Authorization": f"Bearer {user_token}"}

        plan_id = sample_plans["active_public_plan"].id

        # Regular user can access active plan
        response = test_client.get(f"/api/v1/plans/{plan_id}", headers=user_headers)
        assert response.status_code == status.HTTP_200_OK

        # Admin deactivates the plan
        response = test_client.delete(f"/api/v1/plans/{plan_id}", headers=admin_headers)
        assert response.status_code == status.HTTP_200_OK

        # Regular user can no longer access deactivated plan
        response = test_client.get(f"/api/v1/plans/{plan_id}", headers=user_headers)
        assert response.status_code == status.HTTP_404_NOT_FOUND

        # Admin can still access deactivated plan
        response = test_client.get(f"/api/v1/plans/{plan_id}", headers=admin_headers)
        assert response.status_code == status.HTTP_200_OK

    def test_pagination_edge_cases(self, test_client, created_test_admin, bulk_plans):
        """Test pagination edge cases."""
        # Login as admin
        login_data = {
            "email": created_test_admin.email,
            "password": "AdminPassword123!",  # pragma: allowlist secret
            "remember_me": False,
        }
        login_response = test_client.post("/api/v1/auth/login", json=login_data)
        token = login_response.json()["data"]["access_token"]
        headers = {"Authorization": f"Bearer {token}"}

        # Test page beyond available data
        response = test_client.get("/api/v1/plans/?page=100&page_size=10", headers=headers)
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["plans"] == []
        assert data["total"] == 25
        assert data["page"] == 100

        # Test very large page size
        response = test_client.get("/api/v1/plans/?page=1&page_size=100", headers=headers)
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert len(data["plans"]) == 25  # All available plans
        assert data["total"] == 25

        # Test page size of 1
        response = test_client.get("/api/v1/plans/?page=1&page_size=1", headers=headers)
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert len(data["plans"]) == 1
        assert data["total_pages"] == 25

    def test_create_plan_zero_prices(self, test_client, created_test_admin):
        """Test creating plan with zero prices (free plan)."""
        # Login as admin
        login_data = {
            "email": created_test_admin.email,
            "password": "AdminPassword123!",  # pragma: allowlist secret
            "remember_me": False,
        }
        login_response = test_client.post("/api/v1/auth/login", json=login_data)
        token = login_response.json()["data"]["access_token"]
        headers = {"Authorization": f"Bearer {token}"}

        # Create free plan
        plan_data = {
            "name": "Free Plan",
            "plan_code": "free-plan",
            "price_per_month": 0.0,
            "price_per_year": 0.0,
            "features": {"tier": "free"},
        }
        response = test_client.post("/api/v1/plans/", json=plan_data, headers=headers)

        assert response.status_code == status.HTTP_201_CREATED
        data = response.json()
        assert data["data"]["price_per_month"] == 0.0
        assert data["data"]["price_per_year"] == 0.0

    def test_create_plan_null_prices(self, test_client, created_test_admin):
        """Test creating plan with null prices should fail validation."""
        # Login as admin
        login_data = {
            "email": created_test_admin.email,
            "password": "AdminPassword123!",  # pragma: allowlist secret
            "remember_me": False,
        }
        login_response = test_client.post("/api/v1/auth/login", json=login_data)
        token = login_response.json()["data"]["access_token"]
        headers = {"Authorization": f"Bearer {token}"}

        # Try to create plan with null prices (should fail)
        plan_data = {
            "name": "Enterprise Plan",
            "plan_code": "ENTERPRISE_PLAN",
            "price_per_month": None,
            "price_per_year": None,
            "features": {"tier": "enterprise"},
        }
        response = test_client.post("/api/v1/plans/", json=plan_data, headers=headers)

        # Should fail validation because at least one price is required
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    def test_update_plan_empty_request(self, test_client, created_test_admin, sample_plans):
        """Test updating plan with empty request body."""
        # Login as admin
        login_data = {
            "email": created_test_admin.email,
            "password": "AdminPassword123!",  # pragma: allowlist secret
            "remember_me": False,
        }
        login_response = test_client.post("/api/v1/auth/login", json=login_data)
        token = login_response.json()["data"]["access_token"]
        headers = {"Authorization": f"Bearer {token}"}

        # Update with empty body (should succeed but change nothing)
        plan_id = sample_plans["active_public_plan"].id
        original_name = sample_plans["active_public_plan"].name
        response = test_client.put(f"/api/v1/plans/{plan_id}", json={}, headers=headers)

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["data"]["name"] == original_name  # Should remain unchanged

    def test_concurrent_plan_creation_same_code(self, test_client, created_test_admin):
        """Test concurrent plan creation with same plan_code."""
        # Login as admin
        login_data = {
            "email": created_test_admin.email,
            "password": "AdminPassword123!",  # pragma: allowlist secret
            "remember_me": False,
        }
        login_response = test_client.post("/api/v1/auth/login", json=login_data)
        token = login_response.json()["data"]["access_token"]
        headers = {"Authorization": f"Bearer {token}"}

        plan_data = {
            "name": "Concurrent Plan",
            "plan_code": "CONCURRENT_PLAN",
            "price_per_month": 19.99,
            "features": {"test": True},
        }

        # First request should succeed
        response1 = test_client.post("/api/v1/plans/", json=plan_data, headers=headers)
        assert response1.status_code == status.HTTP_201_CREATED

        # Second request with same plan_code should fail
        response2 = test_client.post("/api/v1/plans/", json=plan_data, headers=headers)
        assert response2.status_code == status.HTTP_400_BAD_REQUEST

    def test_plan_versioning_workflow(self, test_client, created_test_admin):
        """Test complete plan versioning workflow."""
        # Login as admin
        login_data = {
            "email": created_test_admin.email,
            "password": "AdminPassword123!",  # pragma: allowlist secret
            "remember_me": False,
        }
        login_response = test_client.post("/api/v1/auth/login", json=login_data)
        token = login_response.json()["data"]["access_token"]
        headers = {"Authorization": f"Bearer {token}"}

        # Create v1 plan
        v1_data = {
            "name": "Basic Plan",
            "plan_code": "basic-plan-v1",
            "price_per_month": 10.0,
            "features": {"version": "1.0"},
        }
        response = test_client.post("/api/v1/plans/", json=v1_data, headers=headers)
        assert response.status_code == status.HTTP_201_CREATED
        v1_plan_id = response.json()["data"]["id"]

        # Create v2 plan with same name but different code
        v2_data = {
            "name": "Basic Plan",  # Same name
            "plan_code": "basic-plan-v2",  # Different code
            "price_per_month": 15.0,
            "features": {"version": "2.0"},
        }
        response = test_client.post("/api/v1/plans/", json=v2_data, headers=headers)
        assert response.status_code == status.HTTP_201_CREATED

        # Deactivate v1 plan
        response = test_client.delete(f"/api/v1/plans/{v1_plan_id}", headers=headers)
        assert response.status_code == status.HTTP_200_OK

        # List plans should show both versions for admin
        response = test_client.get("/api/v1/plans/", headers=headers)
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        plan_names = [plan["name"] for plan in data["plans"]]
        assert plan_names.count("Basic Plan") == 2  # Both versions

    def test_extreme_pagination_values(self, test_client, created_test_admin):
        """Test pagination with extreme values."""
        # Login as admin
        login_data = {
            "email": created_test_admin.email,
            "password": "AdminPassword123!",  # pragma: allowlist secret
            "remember_me": False,
        }
        login_response = test_client.post("/api/v1/auth/login", json=login_data)
        token = login_response.json()["data"]["access_token"]
        headers = {"Authorization": f"Bearer {token}"}

        # Test with page 0 (should be handled gracefully)
        response = test_client.get("/api/v1/plans/?page=0", headers=headers)
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

        # Test with negative page
        response = test_client.get("/api/v1/plans/?page=-1", headers=headers)
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

        # Test with page_size 0
        response = test_client.get("/api/v1/plans/?page_size=0", headers=headers)
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

        # Test with page_size exceeding limit (101)
        response = test_client.get("/api/v1/plans/?page_size=101", headers=headers)
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
