"""Add plans table

Revision ID: 478495380da1
Revises: d402855a0828
Create Date: 2025-07-23 17:19:46.853587

"""
from collections.abc import Sequence

import sqlalchemy as sa
import sqlmodel

from alembic import op

# revision identifiers, used by Alembic.
revision: str = '478495380da1'
down_revision: str | Sequence[str] | None = 'd402855a0828'
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('plans',
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
    sa.Column('created_by', sa.Uuid(), nullable=True),
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('name', sqlmodel.sql.sqltypes.AutoString(length=64), nullable=False),
    sa.Column('plan_code', sqlmodel.sql.sqltypes.AutoString(length=64), nullable=False),
    sa.Column('description', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('price_per_month', sa.Float(), nullable=True),
    sa.Column('price_per_year', sa.Float(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('is_public', sa.Boolean(), nullable=False),
    sa.Column('trial_available', sa.Boolean(), nullable=False),
    sa.Column('trial_duration_days', sa.Integer(), nullable=True),
    sa.Column('features', sa.JSON(), nullable=False),
    sa.ForeignKeyConstraint(['created_by'], ['accounts.id'], ),
    sa.PrimaryKeyConstraint('id', name='plan_pkey'),
    sa.UniqueConstraint('name'),
    sa.UniqueConstraint('plan_code')
    )
    op.create_index('plan_active_idx', 'plans', ['is_active'], unique=False)
    op.create_index('plan_code_idx', 'plans', ['plan_code'], unique=False)
    op.create_index('plan_name_idx', 'plans', ['name'], unique=False)
    op.create_index('plan_public_idx', 'plans', ['is_public'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('plan_public_idx', table_name='plans')
    op.drop_index('plan_name_idx', table_name='plans')
    op.drop_index('plan_code_idx', table_name='plans')
    op.drop_index('plan_active_idx', table_name='plans')
    op.drop_table('plans')
    # ### end Alembic commands ###
