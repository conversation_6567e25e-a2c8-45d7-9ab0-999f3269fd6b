"""All other models migration

Revision ID: 61b483323e8a
Revises: b391f63890ae
Create Date: 2025-07-28 15:40:54.025333

"""
from collections.abc import Sequence

import sqlalchemy as sa
import sqlmodel

from alembic import op

# revision identifiers, used by Alembic.
revision: str = '61b483323e8a'
down_revision: str | Sequence[str] | None = 'b391f63890ae'
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('audit_logs',
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
    sa.Column('created_by', sa.Uuid(), nullable=True),
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('actor_id', sa.Uuid(), nullable=True),
    sa.Column('action', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('target_id', sa.Uuid(), nullable=False),
    sa.Column('target_type', sqlmodel.sql.sqltypes.AutoString(length=64), nullable=False),
    sa.Column('success', sa.Boolean(), nullable=False),
    sa.Column('remarks', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.ForeignKeyConstraint(['actor_id'], ['accounts.id'], ),
    sa.ForeignKeyConstraint(['created_by'], ['accounts.id'], ),
    sa.PrimaryKeyConstraint('id', name='audit_log_pkey')
    )
    op.create_index('audit_log_action_idx', 'audit_logs', ['action'], unique=False)
    op.create_index('audit_log_actor_idx', 'audit_logs', ['actor_id'], unique=False)
    op.create_index('audit_log_created_at_idx', 'audit_logs', ['created_at'], unique=False)
    op.create_index('audit_log_success_idx', 'audit_logs', ['success'], unique=False)
    op.create_index('audit_log_target_idx', 'audit_logs', ['target_id', 'target_type'], unique=False)
    op.create_table('workspaces',
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
    sa.Column('created_by', sa.Uuid(), nullable=True),
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('name', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('status', sa.Enum('ACTIVE', 'ARCHIVED', name='workspacestatus'), nullable=False),
    sa.Column('owner_id', sa.Uuid(), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=False),
    sa.ForeignKeyConstraint(['created_by'], ['accounts.id'], ),
    sa.ForeignKeyConstraint(['owner_id'], ['accounts.id'], ),
    sa.PrimaryKeyConstraint('id', name='workspace_pkey')
    )
    op.create_index('workspace_deleted_idx', 'workspaces', ['is_deleted'], unique=False)
    op.create_index('workspace_name_idx', 'workspaces', ['name'], unique=False)
    op.create_index('workspace_owner_idx', 'workspaces', ['owner_id'], unique=False)
    op.create_index('workspace_status_idx', 'workspaces', ['status'], unique=False)
    op.create_table('subscriptions',
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
    sa.Column('created_by', sa.Uuid(), nullable=True),
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('workspace_id', sa.Uuid(), nullable=False),
    sa.Column('plan_id', sa.Uuid(), nullable=False),
    sa.Column('billing_cycle', sa.Enum('MONTHLY', 'YEARLY', 'TRIAL', name='billingcycle'), nullable=False),
    sa.Column('start_date', sa.Date(), nullable=True),
    sa.Column('end_date', sa.Date(), nullable=True),
    sa.Column('status', sa.Enum('ACTIVE', 'CANCELLED', 'EXPIRED', name='subscriptionstatus'), nullable=False),
    sa.Column('is_trial', sa.Boolean(), nullable=False),
    sa.Column('is_current', sa.Boolean(), nullable=False),
    sa.Column('previous_subscription_id', sa.Uuid(), nullable=True),
    sa.ForeignKeyConstraint(['created_by'], ['accounts.id'], ),
    sa.ForeignKeyConstraint(['plan_id'], ['plans.id'], ),
    sa.ForeignKeyConstraint(['previous_subscription_id'], ['subscriptions.id'], ),
    sa.ForeignKeyConstraint(['workspace_id'], ['workspaces.id'], ),
    sa.PrimaryKeyConstraint('id', name='subscription_pkey')
    )
    op.create_index('subscription_current_idx', 'subscriptions', ['is_current'], unique=False)
    op.create_index('subscription_dates_idx', 'subscriptions', ['start_date', 'end_date'], unique=False)
    op.create_index('subscription_plan_idx', 'subscriptions', ['plan_id'], unique=False)
    op.create_index('subscription_previous_idx', 'subscriptions', ['previous_subscription_id'], unique=False)
    op.create_index('subscription_status_idx', 'subscriptions', ['status'], unique=False)
    op.create_index('subscription_trial_idx', 'subscriptions', ['is_trial'], unique=False)
    op.create_index('subscription_workspace_idx', 'subscriptions', ['workspace_id'], unique=False)
    op.create_table('workspace_members',
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
    sa.Column('created_by', sa.Uuid(), nullable=True),
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('workspace_id', sa.Uuid(), nullable=False),
    sa.Column('account_id', sa.Uuid(), nullable=False),
    sa.Column('role', sa.Enum('OWNER', 'ADMIN', 'USER', name='workspacerole'), nullable=False),
    sa.Column('invited_by', sa.Uuid(), nullable=True),
    sa.ForeignKeyConstraint(['account_id'], ['accounts.id'], ),
    sa.ForeignKeyConstraint(['created_by'], ['accounts.id'], ),
    sa.ForeignKeyConstraint(['invited_by'], ['accounts.id'], ),
    sa.ForeignKeyConstraint(['workspace_id'], ['workspaces.id'], ),
    sa.PrimaryKeyConstraint('id', name='workspace_member_pkey'),
    sa.UniqueConstraint('workspace_id', 'account_id', name='uq_workspace_account')
    )
    op.create_index('workspace_member_account_idx', 'workspace_members', ['account_id'], unique=False)
    op.create_index('workspace_member_invited_by_idx', 'workspace_members', ['invited_by'], unique=False)
    op.create_index('workspace_member_role_idx', 'workspace_members', ['role'], unique=False)
    op.create_index('workspace_member_workspace_idx', 'workspace_members', ['workspace_id'], unique=False)
    op.create_table('payments',
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
    sa.Column('created_by', sa.Uuid(), nullable=True),
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('subscription_id', sa.Uuid(), nullable=False),
    sa.Column('paid_at', sa.Date(), nullable=True),
    sa.Column('reference', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('remarks', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('amount_paid', sa.Float(), nullable=False),
    sa.ForeignKeyConstraint(['created_by'], ['accounts.id'], ),
    sa.ForeignKeyConstraint(['subscription_id'], ['subscriptions.id'], ),
    sa.PrimaryKeyConstraint('id', name='payment_pkey'),
    sa.UniqueConstraint('reference'),
    sa.UniqueConstraint('reference', name='uq_payment_reference')
    )
    op.create_index('payment_amount_idx', 'payments', ['amount_paid'], unique=False)
    op.create_index('payment_paid_at_idx', 'payments', ['paid_at'], unique=False)
    op.create_index('payment_reference_idx', 'payments', ['reference'], unique=False)
    op.create_index('payment_subscription_idx', 'payments', ['subscription_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('payment_subscription_idx', table_name='payments')
    op.drop_index('payment_reference_idx', table_name='payments')
    op.drop_index('payment_paid_at_idx', table_name='payments')
    op.drop_index('payment_amount_idx', table_name='payments')
    op.drop_table('payments')
    op.drop_index('workspace_member_workspace_idx', table_name='workspace_members')
    op.drop_index('workspace_member_role_idx', table_name='workspace_members')
    op.drop_index('workspace_member_invited_by_idx', table_name='workspace_members')
    op.drop_index('workspace_member_account_idx', table_name='workspace_members')
    op.drop_table('workspace_members')
    op.drop_index('subscription_workspace_idx', table_name='subscriptions')
    op.drop_index('subscription_trial_idx', table_name='subscriptions')
    op.drop_index('subscription_status_idx', table_name='subscriptions')
    op.drop_index('subscription_previous_idx', table_name='subscriptions')
    op.drop_index('subscription_plan_idx', table_name='subscriptions')
    op.drop_index('subscription_dates_idx', table_name='subscriptions')
    op.drop_index('subscription_current_idx', table_name='subscriptions')
    op.drop_table('subscriptions')
    op.drop_index('workspace_status_idx', table_name='workspaces')
    op.drop_index('workspace_owner_idx', table_name='workspaces')
    op.drop_index('workspace_name_idx', table_name='workspaces')
    op.drop_index('workspace_deleted_idx', table_name='workspaces')
    op.drop_table('workspaces')
    op.drop_index('audit_log_target_idx', table_name='audit_logs')
    op.drop_index('audit_log_success_idx', table_name='audit_logs')
    op.drop_index('audit_log_created_at_idx', table_name='audit_logs')
    op.drop_index('audit_log_actor_idx', table_name='audit_logs')
    op.drop_index('audit_log_action_idx', table_name='audit_logs')
    op.drop_table('audit_logs')
    # ### end Alembic commands ###
