"""remove unique constraint from plan name to support versioning

Revision ID: b391f63890ae
Revises: 478495380da1
Create Date: 2025-07-24 11:39:20.526074

"""
from collections.abc import Sequence

from alembic import op

# revision identifiers, used by Alembic.
revision: str = 'b391f63890ae'
down_revision: str | Sequence[str] | None = '478495380da1'
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(op.f('plans_name_key'), 'plans', type_='unique')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_unique_constraint(op.f('plans_name_key'), 'plans', ['name'], postgresql_nulls_not_distinct=False)
    # ### end Alembic commands ###
