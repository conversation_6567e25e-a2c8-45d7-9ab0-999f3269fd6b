"""Plan service for subscription plan management."""

import logging
from uuid import UUID

from sqlmodel import Session, select

from ..entities.plan import Plan
from ..exceptions import AccountError
from ..models.plan import PlanCreateRequest, PlanUpdateRequest
from ..utils.error_factory import ErrorFactory
from ..utils.pagination import paginate_sqlmodel_query

logger = logging.getLogger(__name__)


class PlanService:
    """
    Plan service providing CRUD operations for subscription plans.

    This service handles plan creation, retrieval, updates, and deletion
    with proper validation and error handling.
    """

    def __init__(self, db_session: Session):
        """
        Initialize plan service.

        Args:
            db_session: Database session for plan operations
        """
        self.db_session = db_session

    def create_plan(self, request: PlanCreateRequest, created_by: UUID | None = None) -> Plan:
        """
        Create a new subscription plan.

        Args:
            request: Plan creation request data
            created_by: ID of user creating the plan

        Returns:
            Plan: Created plan entity

        Raises:
            AccountError: If plan validation fails
            DatabaseError: If database operation fails
        """
        try:
            # Normalize trial settings
            trial_duration_days = request.trial_duration_days
            if not request.trial_available and trial_duration_days == 0:
                # Convert 0 to None when trial is not available
                trial_duration_days = None

            # Validate trial settings
            if request.trial_available and trial_duration_days is None:
                raise AccountError(
                    message="Trial duration days is required when trial is available",
                    status_code=400,
                    error_code="TRIAL_DURATION_REQUIRED",
                    context={"field": "trial_duration_days", "reason": "required_for_trial"},
                )

            if not request.trial_available and trial_duration_days is not None and trial_duration_days > 0:
                raise AccountError(
                    message="Trial duration days should not be set when trial is not available",
                    status_code=400,
                    error_code="TRIAL_DURATION_NOT_ALLOWED",
                    context={"field": "trial_duration_days", "reason": "not_allowed_without_trial"},
                )

            # Check if plan code already exists (plan codes must be unique)
            existing_code = self.db_session.exec(select(Plan).where(Plan.plan_code == request.plan_code)).first()
            if existing_code:
                raise AccountError(
                    message=f"Plan with code '{request.plan_code}' already exists",
                    status_code=400,
                    error_code="PLAN_CODE_EXISTS",
                    context={"field": "plan_code", "reason": "duplicate", "value": request.plan_code},
                )

            # Create new plan
            plan = Plan(
                name=request.name,
                plan_code=request.plan_code,
                description=request.description,
                price_per_month=request.price_per_month,
                price_per_year=request.price_per_year,
                is_active=request.is_active,
                is_public=request.is_public,
                trial_available=request.trial_available,
                trial_duration_days=trial_duration_days,  # Use normalized value
                features=request.features,
                created_by=created_by,
            )

            self.db_session.add(plan)
            self.db_session.commit()
            self.db_session.refresh(plan)

            logger.info(f"Created plan: {plan.name} (code: {plan.plan_code})")
            return plan

        except AccountError:
            # Re-raise account errors
            raise
        except Exception as e:
            logger.exception(f"Error creating plan: {request.name}")
            self.db_session.rollback()
            raise ErrorFactory.create_database_error(
                message="Failed to create plan", context={"plan_name": request.name, "error": str(e)}
            ) from e

    def get_plan_by_id(self, plan_id: UUID) -> Plan | None:
        """
        Get plan by ID.

        Args:
            plan_id: Plan ID

        Returns:
            Plan: Plan entity or None if not found
        """
        try:
            return self.db_session.exec(select(Plan).where(Plan.id == plan_id)).first()
        except Exception as e:
            logger.exception(f"Error getting plan by ID: {plan_id}")
            raise ErrorFactory.create_database_error(
                message="Failed to retrieve plan", context={"plan_id": str(plan_id), "error": str(e)}
            ) from e

    def get_plan_by_id_filtered(
        self,
        plan_id: UUID,
        active_only: bool = False,
        public_only: bool = False,
    ) -> Plan | None:
        """
        Get plan by ID with filtering options.

        Args:
            plan_id: Plan ID
            active_only: Filter to active plans only
            public_only: Filter to public plans only

        Returns:
            Plan: Plan entity or None if not found or filtered out
        """
        try:
            query = select(Plan).where(Plan.id == plan_id)

            if active_only:
                query = query.where(Plan.is_active)

            if public_only:
                query = query.where(Plan.is_public)

            return self.db_session.exec(query).first()
        except Exception as e:
            logger.exception(f"Error getting plan by ID with filters: {plan_id}")
            raise ErrorFactory.create_database_error(
                message="Failed to retrieve plan", context={"plan_id": str(plan_id), "error": str(e)}
            ) from e

    def get_plan_by_code(self, plan_code: str) -> Plan | None:
        """
        Get plan by code.

        Args:
            plan_code: Plan code (case-insensitive)

        Returns:
            Plan: Plan entity or None if not found
        """
        try:
            # Normalize plan code to uppercase for consistent searching
            normalized_plan_code = plan_code.upper()
            return self.db_session.exec(select(Plan).where(Plan.plan_code == normalized_plan_code)).first()
        except Exception as e:
            logger.exception(f"Error getting plan by code: {plan_code}")
            raise ErrorFactory.create_database_error(
                message="Failed to retrieve plan", context={"plan_code": plan_code, "error": str(e)}
            ) from e

    def get_plan_by_code_filtered(
        self,
        plan_code: str,
        active_only: bool = False,
        public_only: bool = False,
    ) -> Plan | None:
        """
        Get plan by code with filtering options.

        Args:
            plan_code: Plan code (case-insensitive)
            active_only: Filter to active plans only
            public_only: Filter to public plans only

        Returns:
            Plan: Plan entity or None if not found or filtered out
        """
        try:
            # Normalize plan code to uppercase for consistent searching
            normalized_plan_code = plan_code.upper()
            query = select(Plan).where(Plan.plan_code == normalized_plan_code)

            if active_only:
                query = query.where(Plan.is_active)

            if public_only:
                query = query.where(Plan.is_public)

            return self.db_session.exec(query).first()
        except Exception as e:
            logger.exception(f"Error getting plan by code with filters: {plan_code}")
            raise ErrorFactory.create_database_error(
                message="Failed to retrieve plan", context={"plan_code": plan_code, "error": str(e)}
            ) from e

    def list_plans(
        self, page: int = 1, page_size: int = 50, active_only: bool = False, public_only: bool = False
    ) -> tuple[list[Plan], int]:
        """
        List plans with pagination and filtering.

        Args:
            page: Page number (1-based)
            page_size: Number of plans per page
            active_only: Filter to active plans only
            public_only: Filter to public plans only

        Returns:
            tuple: (list of plans, total count)
        """
        try:
            # Build query
            query = select(Plan)

            if active_only:
                query = query.where(Plan.is_active)

            if public_only:
                query = query.where(Plan.is_public)

            # Order by creation date (newest first)
            query = query.order_by(Plan.created_at.desc())

            # Use pagination utility function
            plans, pagination_meta = paginate_sqlmodel_query(self.db_session, query, page, page_size, "standard")

            return list(plans), pagination_meta.total_items

        except Exception as e:
            logger.exception("Error listing plans")
            raise ErrorFactory.create_database_error(
                message="Failed to list plans", context={"page": page, "page_size": page_size, "error": str(e)}
            ) from e

    def update_plan(self, plan_id: UUID, request: PlanUpdateRequest) -> Plan:
        """
        Update an existing plan.

        Args:
            plan_id: Plan ID to update
            request: Plan update request data

        Returns:
            Plan: Updated plan entity

        Raises:
            AccountError: If plan not found or validation fails
            DatabaseError: If database operation fails
        """
        try:
            # Get existing plan
            plan = self.get_plan_by_id(plan_id)
            if not plan:
                raise AccountError(
                    message=f"Plan with ID '{plan_id}' not found",
                    status_code=404,
                    error_code="PLAN_NOT_FOUND",
                    context={"plan_id": str(plan_id), "reason": "not_found"},
                )

            # Normalize trial settings - if disabling trial or setting duration to 0, clear trial_duration_days
            update_trial_duration = False
            if request.trial_available is False or request.trial_duration_days == 0:
                request.trial_duration_days = None
                update_trial_duration = True

            # Validate trial settings if being updated
            trial_available = request.trial_available if request.trial_available is not None else plan.trial_available

            # For trial_duration_days, use the request value if explicitly provided (including None after normalization)
            # Otherwise, use the existing plan value
            if request.trial_available is not None or request.trial_duration_days is not None:
                # If trial settings are being updated, use the normalized request value
                trial_duration_days = request.trial_duration_days
            else:
                # If trial settings are not being updated, use existing plan value
                trial_duration_days = plan.trial_duration_days

            if trial_available and trial_duration_days is None:
                raise AccountError(
                    message="Trial duration days is required when trial is available",
                    status_code=400,
                    error_code="TRIAL_DURATION_REQUIRED",
                    context={"field": "trial_duration_days", "reason": "required_for_trial"},
                )

            if not trial_available and trial_duration_days is not None and trial_duration_days > 0:
                raise AccountError(
                    message="Trial duration days should not be set when trial is not available",
                    status_code=400,
                    error_code="TRIAL_DURATION_NOT_ALLOWED",
                    context={"field": "trial_duration_days", "reason": "not_allowed_without_trial"},
                )

            # Check for duplicate code (if code is being updated)
            if request.plan_code and request.plan_code != plan.plan_code:
                existing_code = self.db_session.exec(
                    select(Plan).where(Plan.plan_code == request.plan_code, Plan.id != plan_id)
                ).first()
                if existing_code:
                    raise AccountError(
                        message=f"Plan with code '{request.plan_code}' already exists",
                        status_code=400,
                        error_code="PLAN_CODE_EXISTS",
                        context={"field": "plan_code", "reason": "duplicate", "value": request.plan_code},
                    )

            # Update plan fields
            if request.name is not None:
                plan.name = request.name
            if request.plan_code is not None:
                plan.plan_code = request.plan_code
            if request.description is not None:
                plan.description = request.description
            if request.price_per_month is not None:
                plan.price_per_month = request.price_per_month
            if request.price_per_year is not None:
                plan.price_per_year = request.price_per_year
            if request.is_active is not None:
                plan.is_active = request.is_active
            if request.is_public is not None:
                plan.is_public = request.is_public
            if request.trial_available is not None:
                plan.trial_available = request.trial_available

            # Handle trial_duration_days update (including when normalized to None)
            if update_trial_duration or request.trial_duration_days is not None:
                plan.trial_duration_days = request.trial_duration_days
            if request.features is not None:
                plan.features = request.features

            self.db_session.commit()
            self.db_session.refresh(plan)

            logger.info(f"Updated plan: {plan.name} (ID: {plan_id})")
            return plan

        except AccountError:
            # Re-raise account errors
            raise
        except Exception as e:
            logger.exception(f"Error updating plan: {plan_id}")
            self.db_session.rollback()
            raise ErrorFactory.create_database_error(
                message="Failed to update plan", context={"plan_id": str(plan_id), "error": str(e)}
            ) from e

    def delete_plan(self, plan_id: UUID) -> bool:
        """
        Delete a plan (soft delete by setting is_active to False).

        Args:
            plan_id: Plan ID to delete

        Returns:
            bool: True if plan was deleted

        Raises:
            AccountError: If plan not found
            DatabaseError: If database operation fails
        """
        try:
            # Get existing plan
            plan = self.get_plan_by_id(plan_id)
            if not plan:
                raise AccountError(
                    message=f"Plan with ID '{plan_id}' not found",
                    status_code=404,
                    error_code="PLAN_NOT_FOUND",
                    context={"plan_id": str(plan_id), "reason": "not_found"},
                )

            # Soft delete the plan by setting is_active to False
            plan.is_active = False
            self.db_session.commit()
            self.db_session.refresh(plan)

            logger.info(f"Soft deleted plan: {plan.name} (ID: {plan_id})")
            return True

        except AccountError:
            # Re-raise account errors
            raise
        except Exception as e:
            logger.exception(f"Error deleting plan: {plan_id}")
            self.db_session.rollback()
            raise ErrorFactory.create_database_error(
                message="Failed to delete plan", context={"plan_id": str(plan_id), "error": str(e)}
            ) from e


def get_plan_service(db_session: Session) -> PlanService:
    """
    Get plan service instance for dependency injection.

    Args:
        db_session: Database session

    Returns:
        PlanService: Plan service instance
    """
    return PlanService(db_session)
