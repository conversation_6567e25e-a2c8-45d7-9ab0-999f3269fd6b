"""Authentication service for user login and session management."""

import logging
from datetime import UTC, datetime

# datetime imports moved to Account model
from uuid import UUID

from sqlmodel import Session

from ..entities.account import Account
from ..entities.status import AccountStatus
from ..events.dispatcher import (
    emit_login_event,
    emit_login_failed_event,
    emit_registration_event,
)
from ..events.models import (
    LoginEventContext,
    LoginFailedEventContext,
    RegistrationEventContext,
)
from ..exceptions import (
    AccountBannedError,
    AccountClosedError,
    AccountError,
    AccountLoginError,
    AccountNotVerifiedError,
    AuthenticationError,
    RateLimitExceededError,
)
from ..libs.password import (
    create_password_hash,
    validate_password_strength,
    verify_password,
)
from ..models.auth import PasswordChangeResponse
from ..models.token import TokenPair
from ..services.token_service import get_token_service
from ..utils.error_factory import ErrorFactory
from ..utils.rate_limiter import get_login_rate_limiter

logger = logging.getLogger(__name__)


class AuthService:
    """
    Authentication service providing login functionality.

    This service handles user authentication, password verification,
    and JWT token management with secure password hashing.
    """

    def __init__(self, db_session: Session):
        """
        Initialize authentication service.

        Args:
            db_session: Database session for user operations
        """
        self.db_session = db_session

    def authenticate_user(self, email: str, password: str, login_ip: str | None = None) -> TokenPair:
        """
        Authenticate user with email and password.

        Args:
            email: User email address
            password: Plain text password
            login_ip: IP address of the login attempt

        Returns:
            TokenPair: Access and refresh tokens for authenticated user

        Raises:
            AuthenticationError: If authentication fails
            AccountError: If account is not in valid state
        """
        # Note: Basic validation is handled by LoginRequest model
        login_timestamp = datetime.now(UTC)

        try:
            # Check if rate limiting is enabled
            from ..configs import madcrow_config

            email_normalized = email.strip().lower()
            redis_client = None
            rate_limiter = None

            if madcrow_config.RATE_LIMIT_LOGIN_ENABLED:
                # Get Redis client for rate limiting
                from ..dependencies.redis import get_redis_client

                redis_client = get_redis_client()
                rate_limiter = get_login_rate_limiter()

                # Check rate limiting before any authentication attempts
                if rate_limiter.is_rate_limited(email_normalized, redis_client):
                    retry_after = (
                        rate_limiter.get_time_until_reset(email_normalized, redis_client) or rate_limiter.time_window
                    )
                    logger.warning(f"Rate limit exceeded for login attempt: {email_normalized}")
                    raise RateLimitExceededError(
                        identifier=email_normalized,
                        max_attempts=rate_limiter.max_attempts,
                        time_window=rate_limiter.time_window,
                        retry_after=retry_after,
                    )

            # Find user by email using Account model method
            user = Account.get_by_email(self.db_session, email_normalized)
            if not user:
                logger.warning(f"User not found for email: {email_normalized}")
                # Increment rate limit for failed attempts (user not found)
                if rate_limiter and redis_client:
                    rate_limiter.increment_rate_limit(email_normalized, redis_client)

                # Emit login failed event
                self._emit_login_failed_event(
                    email=email, failure_reason="user_not_found", ip_address=login_ip, timestamp=login_timestamp
                )

                raise ErrorFactory.create_authentication_error(
                    message="Invalid email or password", context={"reason": "user_not_found"}
                )

            # Check account status first (following Dify pattern)
            if user.status == AccountStatus.PENDING:
                self._emit_login_failed_event(
                    email=email, failure_reason="account_not_verified", ip_address=login_ip, timestamp=login_timestamp
                )
                raise AccountNotVerifiedError(email=email, account_id=user.id)

            if user.status == AccountStatus.BANNED:
                self._emit_login_failed_event(
                    email=email, failure_reason="account_banned", ip_address=login_ip, timestamp=login_timestamp
                )
                raise AccountBannedError("Account is banned.", email=email, account_id=user.id)

            if user.status == AccountStatus.CLOSED:
                self._emit_login_failed_event(
                    email=email, failure_reason="account_closed", ip_address=login_ip, timestamp=login_timestamp
                )
                raise AccountClosedError("Account is closed.", email=email, account_id=user.id)

            # Check if account is deleted
            if user.is_deleted:
                self._emit_login_failed_event(
                    email=email, failure_reason="account_deleted", ip_address=login_ip, timestamp=login_timestamp
                )
                raise AccountLoginError("Account has been deleted.", email=email, account_id=user.id)

            # Check if password is set
            if not user.is_password_set:
                self._emit_login_failed_event(
                    email=email, failure_reason="no_password_set", ip_address=login_ip, timestamp=login_timestamp
                )
                raise ErrorFactory.create_authentication_error(
                    message="Account password not set", context={"reason": "no_password"}
                )

            # Final check - account must be active to login
            if not user.is_active:
                self._emit_login_failed_event(
                    email=email, failure_reason="account_inactive", ip_address=login_ip, timestamp=login_timestamp
                )
                raise AccountLoginError(
                    f"Account status is {user.status.value} and cannot login.", email=email, account_id=user.id
                )

            # Validate password complexity AFTER rate limiting but BEFORE verification
            # This prevents information disclosure while still enforcing password policy
            try:
                from ..utils.business_validators import UnifiedValidator

                UnifiedValidator.validate_password(password)
            except Exception:
                logger.warning(f"Password complexity validation failed for email: {email_normalized}")
                # Increment rate limit for failed attempts (invalid password format)
                if rate_limiter and redis_client:
                    rate_limiter.increment_rate_limit(email_normalized, redis_client)

                self._emit_login_failed_event(
                    email=email, failure_reason="invalid_credentials", ip_address=login_ip, timestamp=login_timestamp
                )

                # Return generic error message to prevent information disclosure
                raise ErrorFactory.create_authentication_error(
                    message="Invalid email or password", context={"reason": "invalid_credentials"}
                )

            # Verify password
            if not self._verify_password(password, user.password, user.password_salt):
                logger.warning(f"Failed login attempt for email: {email_normalized}")
                # Increment rate limit for failed password attempts
                if rate_limiter and redis_client:
                    rate_limiter.increment_rate_limit(email_normalized, redis_client)
                logger.warning(f"Failed login attempt for email: {email}")

                self._emit_login_failed_event(
                    email=email, failure_reason="invalid_credentials", ip_address=login_ip, timestamp=login_timestamp
                )

                raise ErrorFactory.create_authentication_error(
                    message="Invalid email or password", context={"reason": "invalid_credentials"}
                )

            # Successful authentication - reset rate limit
            if rate_limiter and redis_client:
                rate_limiter.reset_rate_limit(email_normalized, redis_client)

            # Update last login information using Account model method
            user.update_last_login(login_ip)
            self.db_session.commit()

            # Create token pair with Redis support
            if not redis_client:
                from ..dependencies.redis import get_redis_client

                redis_client = get_redis_client()
            token_service = get_token_service(redis_client)
            token_pair = token_service.create_token_pair(user)

            # Emit successful login event
            self._emit_login_success_event(
                user=user, ip_address=login_ip, timestamp=login_timestamp, session_duration=token_pair.expires_in
            )

            logger.info(f"Successful login for user: {user.email}")
            return token_pair

        except (
            AuthenticationError,
            AccountError,
            AccountNotVerifiedError,
            AccountBannedError,
            AccountClosedError,
            AccountLoginError,
            RateLimitExceededError,
        ):
            # Re-raise our custom errors
            raise
        except Exception as e:
            logger.exception(f"Unexpected error during authentication for email: {email}")

            # Emit login failed event for system errors
            self._emit_login_failed_event(
                email=email, failure_reason="system_error", ip_address=login_ip, timestamp=login_timestamp
            )

            raise ErrorFactory.create_authentication_error(
                message="Authentication failed due to system error",
                context={"error_type": type(e).__name__, "error_message": str(e)},
            )

    def _get_user_by_email(self, email: str) -> Account:
        """
        Get user account by email address.

        Args:
            email: User email address

        Returns:
            Account: User account

        Raises:
            AuthenticationError: If user not found
        """
        try:
            # Use Account model method for consistency
            user = Account.get_by_email(self.db_session, email.strip().lower())

            if not user:
                logger.warning(f"User not found for email: {email}")
                raise ErrorFactory.create_authentication_error(
                    message="Invalid email or password", context={"reason": "user_not_found"}
                )

            return user

        except AuthenticationError:
            # Re-raise authentication errors
            raise
        except Exception as e:
            logger.exception(f"Database error while fetching user: {email}")
            raise ErrorFactory.create_authentication_error(
                message="Failed to retrieve user information",
                context={"error_type": type(e).__name__, "error_message": str(e)},
            )

    def _verify_password(self, password: str, stored_password: str | None, salt: str | None) -> bool:
        """
        Verify password against stored password and salt.

        Args:
            password: Plain text password
            stored_password: Stored hashed password
            salt: Password salt

        Returns:
            bool: True if password is valid, False otherwise
        """
        if not stored_password or not salt:
            logger.warning("User has no password or salt set")
            return False

        try:
            return verify_password(password, stored_password, salt)
        except Exception:
            logger.exception("Error verifying password")
            return False

    def _emit_login_success_event(
        self,
        user: Account,
        ip_address: str | None = None,
        timestamp: datetime | None = None,
        session_duration: int | None = None,
    ) -> None:
        """
        Emit a successful login event.

        Args:
            user: Authenticated user account
            ip_address: Client IP address
            timestamp: Login timestamp
            session_duration: Session duration in seconds
        """
        try:
            context = LoginEventContext(
                user_id=user.id,
                email=user.email,
                name=user.name,
                is_admin=user.is_admin,
                remember_me=False,  # This would need to be passed from the request
                ip_address=ip_address,
                timestamp=timestamp or datetime.now(UTC),
                session_duration=session_duration,
            )

            emit_login_event(context, sender=self)

        except Exception as e:
            # Don't let event emission failures break the login flow
            logger.warning(f"Failed to emit login success event for user {user.email}: {e}")

    def _emit_login_failed_event(
        self,
        email: str,
        failure_reason: str,
        ip_address: str | None = None,
        timestamp: datetime | None = None,
        attempt_count: int | None = None,
    ) -> None:
        """
        Emit a login failed event.

        Args:
            email: Attempted email address
            failure_reason: Reason for login failure
            ip_address: Client IP address
            timestamp: Failure timestamp
            attempt_count: Number of failed attempts
        """
        try:
            context = LoginFailedEventContext(
                email=email,
                failure_reason=failure_reason,
                ip_address=ip_address,
                timestamp=timestamp or datetime.now(UTC),
                attempt_count=attempt_count,
            )

            emit_login_failed_event(context, sender=self)

        except Exception as e:
            # Don't let event emission failures break the error flow
            logger.warning(f"Failed to emit login failed event for email {email}: {e}")

    def _emit_registration_event(
        self, user: Account, ip_address: str | None = None, timestamp: datetime | None = None
    ) -> None:
        """
        Emit a user registration event.

        Args:
            user: Newly registered user account
            ip_address: Client IP address
            timestamp: Registration timestamp
        """
        try:
            context = RegistrationEventContext(
                user_id=user.id,
                email=user.email,
                name=user.name,
                account_status=user.status.value,
                ip_address=ip_address,
                timestamp=timestamp or datetime.now(UTC),
            )

            emit_registration_event(context, sender=self)

        except Exception as e:
            # Don't let event emission failures break the registration flow
            logger.warning(f"Failed to emit registration event for user {user.email}: {e}")

    def create_account(self, name: str, email: str, password: str, is_admin: bool = False) -> TokenPair:
        """
        Create new user account with secure password handling.

        Args:
            name: User full name
            email: User email address
            password: Plain text password
            is_admin: Whether user should have admin privileges

        Returns:
            TokenPair: Access and refresh tokens for new user

        Raises:
            AuthenticationError: If account creation fails
            AccountError: If account validation fails
        """
        # Note: All validation is now handled by RegisterRequest model
        # Password strength, email format, name validation all happen at model level

        try:
            # Check if email already exists using Account model method
            if Account.email_exists(self.db_session, email.strip().lower()):
                raise ErrorFactory.create_authentication_error(
                    message="Email address is already registered", context={"field": "email", "reason": "duplicate"}
                )

            # Create password hash and salt
            hashed_password, password_salt = create_password_hash(password)

            # Create new account
            new_account = Account(
                name=name.strip(),
                email=email.strip().lower(),
                password=hashed_password,
                password_salt=password_salt,
                status=AccountStatus.ACTIVE,  # Auto-activate for now
                is_admin=is_admin,
                timezone="UTC",
            )

            # Activate the account using Account model method
            new_account.activate()

            # Save to database
            self.db_session.add(new_account)
            self.db_session.commit()
            self.db_session.refresh(new_account)

            # Create token pair with Redis support
            from ..dependencies.redis import get_redis_client

            redis_client = get_redis_client()
            token_service = get_token_service(redis_client)
            token_pair = token_service.create_token_pair(new_account)

            # Emit registration event
            self._emit_registration_event(user=new_account, timestamp=datetime.now(UTC))

            logger.info(f"Created new account for user: {new_account.email}")
            return token_pair

        except (
            AuthenticationError,
            AccountError,
            AccountNotVerifiedError,
            AccountBannedError,
            AccountClosedError,
            AccountLoginError,
        ):
            # Re-raise our custom errors
            raise
        except Exception as e:
            logger.exception(f"Unexpected error during account creation for email: {email}")
            self.db_session.rollback()
            raise ErrorFactory.create_authentication_error(
                message="Account creation failed due to system error",
                context={"error_type": type(e).__name__, "error_message": str(e)},
            )

    # Note: Email lookup is now handled by Account.get_by_email() class method

    # Note: Account status validation is now handled by Account.can_login property

    # Note: Last login update is now handled by Account.update_last_login() method

    def get_user_by_id(self, user_id: UUID) -> Account | None:
        """
        Get user account by ID.

        Args:
            user_id: User ID

        Returns:
            Account or None if not found
        """
        try:
            # Use Account model method for consistency
            return Account.get_by_id(self.db_session, user_id)

        except Exception:
            logger.exception(f"Error fetching user by ID: {user_id}")
            return None

    def is_user_active(self, user_id: UUID) -> bool:
        """
        Check if user is active using Account model method.

        Args:
            user_id: User ID to check

        Returns:
            bool: True if user is active, False otherwise
        """
        user = self.get_user_by_id(user_id)
        return user is not None and user.is_active

    def change_password(self, user_id: UUID, current_password: str, new_password: str) -> PasswordChangeResponse:
        """
        Change user password after verifying current password.

        Args:
            user_id: User ID
            current_password: Current password for verification
            new_password: New password to set

        Returns:
            PasswordChangeResponse: Password change confirmation

        Raises:
            AuthenticationError: If current password is invalid
            AccountError: If account is not found or not active
        """
        from datetime import UTC, datetime

        from ..models.auth import PasswordChangeResponse

        try:
            # Get user account
            user = self.get_user_by_id(user_id)
            if not user:
                raise ErrorFactory.create_authentication_error(
                    message="Account not found", context={"reason": "user_not_found"}
                )

            # Check if account is active
            if not user.is_active:
                raise ErrorFactory.create_authentication_error(
                    message="Account is not active", context={"reason": "account_inactive"}
                )

            # Verify current password
            if not self._verify_password(current_password, user.password, user.password_salt):
                logger.warning(f"Failed password change attempt for user: {user.email}")
                raise ErrorFactory.create_authentication_error(
                    message="Current password is incorrect", context={"reason": "invalid_current_password"}
                )

            # Validate new password strength
            is_valid, error_message = validate_password_strength(new_password)
            if not is_valid:
                raise ErrorFactory.create_authentication_error(
                    message=error_message, context={"field": "new_password", "reason": "weak"}
                )

            # Create new password hash and salt
            hashed_password, password_salt = create_password_hash(new_password)

            # Update password
            user.password = hashed_password
            user.password_salt = password_salt
            user.updated_at = datetime.now(UTC)

            # Commit changes
            self.db_session.commit()

            logger.info(f"Password changed successfully for user: {user.email}")

            return PasswordChangeResponse(
                success=True,
                message="Password changed successfully",
                changed_at=user.updated_at,
            )

        except (AuthenticationError, AccountError):
            # Re-raise our custom errors
            raise
        except Exception as e:
            logger.exception(f"Unexpected error during password change for user_id: {user_id}")
            self.db_session.rollback()
            raise ErrorFactory.create_authentication_error(
                message="Password change failed due to system error",
                context={"error_type": type(e).__name__, "error_message": str(e)},
            )


def get_auth_service(db_session: Session) -> AuthService:
    """
    Factory function to create AuthService instance.

    Args:
        db_session: Database session

    Returns:
        AuthService: Configured authentication service
    """
    return AuthService(db_session)
