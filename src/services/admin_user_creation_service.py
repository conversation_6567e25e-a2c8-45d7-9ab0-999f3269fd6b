"""Service for admin-driven user creation workflow."""

import logging
from datetime import UTC, datetime, timedelta
from uuid import UUID

from sqlmodel import Session, select

from ..entities.account import Account
from ..entities.audit_log import AuditLog
from ..entities.payment import Payment
from ..entities.plan import Plan
from ..entities.status import (
    AccountStatus,
    BillingCycle,
    SubscriptionStatus,
    WorkspaceRole,
    WorkspaceStatus,
)
from ..entities.subscription import Subscription
from ..entities.workspace import Workspace
from ..entities.workspace_member import WorkspaceMember
from ..exceptions import AccountError, DatabaseError
from ..models.admin_user_creation import (
    AdminUserCreationRequest,
    AdminUserCreationResponse,
)
from ..models.audit_log import AuditActions, AuditTargetTypes
from ..utils.error_factory import ErrorFactory

logger = logging.getLogger(__name__)


class AdminUserCreationService:
    """Service for handling admin-driven user creation with workspace and subscription setup."""

    def __init__(self, db_session: Session):
        """Initialize the service with database session."""
        self.db_session = db_session

    def create_user_with_workspace_and_subscription(
        self, request: AdminUserCreationRequest, admin_user: Account
    ) -> AdminUserCreationResponse:
        """
        Create a complete user setup with workspace and subscription.

        This method implements the full workflow:
        1. Create user account (PENDING status, no password)
        2. Create workspace and assign user as owner
        3. Create workspace membership
        4. Create subscription based on plan and billing cycle
        5. Create payment record
        6. Log all actions in audit log

        Args:
            request: User creation request data
            admin_user: Admin user performing the creation

        Returns:
            AdminUserCreationResponse: Complete response with all created entities

        Raises:
            AccountError: If validation fails or entities already exist
            DatabaseError: If database operations fail
        """
        try:
            # Start transaction
            logger.info(f"Admin {admin_user.email} creating user: {request.email}")

            # Step 1: Validate and create user account
            user_account = self._create_user_account(request, admin_user)

            # Step 2: Create workspace
            workspace = self._create_workspace(request, user_account, admin_user)

            # Step 3: Create workspace membership
            self._create_workspace_membership(workspace, user_account, admin_user)

            # Step 4: Get plan and create subscription
            plan = self._get_plan(request.plan_id)
            subscription = self._create_subscription(request, workspace, plan, admin_user)

            # Step 5: Create payment record
            payment = self._create_payment(request, subscription, plan, admin_user)

            # Step 6: Commit all changes
            self.db_session.commit()

            # Step 7: Log success in audit log
            self._log_user_creation_success(user_account, workspace, subscription, payment, admin_user)

            logger.info(
                f"Successfully created user {user_account.email} with workspace {workspace.name} "
                f"and {subscription.billing_cycle} subscription"
            )

            # Build response
            return self._build_response(user_account, workspace, subscription, payment, plan, admin_user)

        except Exception as e:
            # Rollback transaction
            self.db_session.rollback()

            # Log failure in audit log
            self._log_user_creation_failure(request, admin_user, str(e))

            logger.exception(f"Failed to create user {request.email} by admin {admin_user.email}")

            if isinstance(e, AccountError | DatabaseError):
                raise
            else:
                raise ErrorFactory.create_database_error(
                    message="Failed to create user with workspace and subscription",
                    operation="admin_user_creation",
                    cause=e,
                ) from e

    def _create_user_account(self, request: AdminUserCreationRequest, admin_user: Account) -> Account:
        """Create user account with PENDING status and no password."""
        # Check if email already exists
        if Account.email_exists(self.db_session, request.email):
            raise AccountError(
                message=f"User with email '{request.email}' already exists",
                status_code=400,
                error_code="EMAIL_ALREADY_EXISTS",
                context={"email": request.email},
            )

        # Create account
        user_account = Account(
            name=request.name,
            email=request.email.lower().strip(),
            password=None,  # No password - user will set it later
            password_salt=None,
            status=AccountStatus.PENDING,
            is_admin=False,
            timezone="UTC",
            created_by=admin_user.id,
        )

        self.db_session.add(user_account)
        self.db_session.flush()  # Get ID without committing

        # Log user creation
        self._log_audit_action(
            action=AuditActions.USER_CREATED,
            target_id=user_account.id,
            target_type=AuditTargetTypes.USER,
            actor_id=admin_user.id,
            success=True,
            details={"email": user_account.email, "name": user_account.name},
        )

        return user_account

    def _create_workspace(
        self, request: AdminUserCreationRequest, user_account: Account, admin_user: Account
    ) -> Workspace:
        """Create workspace for the user with auto-generated name if not provided."""
        # Auto-generate workspace name if not provided
        workspace_name = request.workspace_name
        if not workspace_name:
            # Generate workspace name from user name: "John Doe" -> "john_doe_workspace"
            clean_name = user_account.name.lower().replace(" ", "_").replace("-", "_")
            # Remove any non-alphanumeric characters except underscores
            clean_name = "".join(c for c in clean_name if c.isalnum() or c == "_")
            workspace_name = f"{clean_name}_workspace"

        workspace = Workspace(
            name=workspace_name, status=WorkspaceStatus.ACTIVE, owner_id=user_account.id, created_by=admin_user.id
        )

        self.db_session.add(workspace)
        self.db_session.flush()  # Get ID without committing

        # Log workspace creation
        self._log_audit_action(
            action=AuditActions.WORKSPACE_CREATED,
            target_id=workspace.id,
            target_type=AuditTargetTypes.WORKSPACE,
            actor_id=admin_user.id,
            success=True,
            details={"name": workspace.name, "owner_id": str(user_account.id)},
        )

        return workspace

    def _create_workspace_membership(
        self, workspace: Workspace, user_account: Account, admin_user: Account
    ) -> WorkspaceMember:
        """Create workspace membership with OWNER role."""
        workspace_member = WorkspaceMember(
            workspace_id=workspace.id, account_id=user_account.id, role=WorkspaceRole.OWNER, invited_by=admin_user.id
        )

        self.db_session.add(workspace_member)
        self.db_session.flush()  # Get ID without committing

        # Log membership creation
        self._log_audit_action(
            action=AuditActions.WORKSPACE_MEMBER_ADDED,
            target_id=workspace_member.id,
            target_type=AuditTargetTypes.WORKSPACE_MEMBER,
            actor_id=admin_user.id,
            success=True,
            details={
                "workspace_id": str(workspace.id),
                "account_id": str(user_account.id),
                "role": workspace_member.role.value,
            },
        )

        return workspace_member

    def _get_plan(self, plan_id: UUID) -> Plan:
        """Get and validate the subscription plan."""
        plan = self.db_session.exec(select(Plan).where(Plan.id == plan_id)).first()

        if not plan:
            raise AccountError(
                message=f"Plan with ID '{plan_id}' not found",
                status_code=404,
                error_code="PLAN_NOT_FOUND",
                context={"plan_id": str(plan_id)},
            )

        if not plan.is_active:
            raise AccountError(
                message=f"Plan '{plan.name}' is not active",
                status_code=400,
                error_code="PLAN_INACTIVE",
                context={"plan_id": str(plan_id), "plan_name": plan.name},
            )

        return plan

    def _create_subscription(
        self, request: AdminUserCreationRequest, workspace: Workspace, plan: Plan, admin_user: Account
    ) -> Subscription:
        """Create subscription based on plan and billing cycle."""
        now = datetime.now(UTC)

        # Use explicit trial choice from admin
        is_trial = request.is_trial

        # Calculate subscription dates
        if is_trial:
            if not plan.trial_available:
                raise AccountError(
                    message=f"Trial is not available for plan '{plan.name}'",
                    status_code=400,
                    error_code="TRIAL_NOT_AVAILABLE",
                    context={"plan_id": str(plan.id), "plan_name": plan.name},
                )

            trial_days = plan.trial_duration_days or 14  # Default to 14 days
            end_date = now + timedelta(days=trial_days)
        else:
            # For paid subscriptions, calculate end date based on billing cycle
            if request.billing_cycle == BillingCycle.MONTHLY:
                end_date = now + timedelta(days=30)
            elif request.billing_cycle == BillingCycle.YEARLY:
                end_date = now + timedelta(days=365)
            else:
                raise AccountError(
                    message=f"Invalid billing cycle for paid subscription: {request.billing_cycle}",
                    status_code=400,
                    error_code="INVALID_BILLING_CYCLE",
                    context={"billing_cycle": str(request.billing_cycle), "is_trial": is_trial},
                )

        # Set billing cycle based on trial status
        if is_trial:
            billing_cycle = BillingCycle.TRIAL
        else:
            billing_cycle = request.billing_cycle

        subscription = Subscription(
            workspace_id=workspace.id,
            plan_id=plan.id,
            status=SubscriptionStatus.ACTIVE,
            billing_cycle=billing_cycle,
            is_trial=is_trial,
            is_current=True,
            start_date=now,
            end_date=end_date,
            created_by=admin_user.id,
        )

        self.db_session.add(subscription)
        self.db_session.flush()  # Get ID without committing

        # Log subscription creation
        self._log_audit_action(
            action=AuditActions.SUBSCRIPTION_CREATED,
            target_id=subscription.id,
            target_type=AuditTargetTypes.SUBSCRIPTION,
            actor_id=admin_user.id,
            success=True,
            details={
                "workspace_id": str(workspace.id),
                "plan_id": str(plan.id),
                "billing_cycle": subscription.billing_cycle.value,
                "is_trial": subscription.is_trial,
            },
        )

        return subscription

    def _create_payment(
        self, request: AdminUserCreationRequest, subscription: Subscription, plan: Plan, admin_user: Account
    ) -> Payment:
        """Create payment record for the subscription with auto-calculated amount and reference."""
        # Auto-calculate amount based on trial status and billing cycle
        if subscription.is_trial:
            amount_paid = 0.0
            payment_reference = f"trial_{str(subscription.id)[:8]}"
        else:
            # Calculate amount based on billing cycle
            if subscription.billing_cycle == BillingCycle.MONTHLY:
                amount_paid = float(plan.price_per_month or 0.0)
                payment_reference = f"monthly_{str(subscription.id)[:8]}"
            elif subscription.billing_cycle == BillingCycle.YEARLY:
                amount_paid = float(plan.price_per_year or 0.0)
                payment_reference = f"yearly_{str(subscription.id)[:8]}"
            else:
                # Fallback for any other billing cycle
                amount_paid = 0.0
                payment_reference = f"other_{str(subscription.id)[:8]}"

        payment = Payment(
            subscription_id=subscription.id,
            amount_paid=amount_paid,
            reference=payment_reference,
            remarks=request.payment_remarks,
            paid_at=datetime.now(UTC).date(),
            created_by=admin_user.id,
        )

        self.db_session.add(payment)
        self.db_session.flush()  # Get ID without committing

        # Log payment creation
        self._log_audit_action(
            action=AuditActions.PAYMENT_CREATED,
            target_id=payment.id,
            target_type=AuditTargetTypes.PAYMENT,
            actor_id=admin_user.id,
            success=True,
            details={
                "subscription_id": str(subscription.id),
                "amount": str(amount_paid),
                "reference": payment_reference,
            },
        )

        return payment

    def _log_audit_action(
        self,
        action: AuditActions,
        target_id: UUID,
        target_type: AuditTargetTypes,
        actor_id: UUID,
        success: bool,
        details: dict | None = None,
    ) -> None:
        """Log action in audit log."""
        audit_log = AuditLog(
            actor_id=actor_id,
            action=action,
            target_id=target_id,
            target_type=target_type,
            success=success,
            details=details or {},
        )

        self.db_session.add(audit_log)

    def _log_user_creation_success(
        self,
        user_account: Account,
        workspace: Workspace,
        subscription: Subscription,
        payment: Payment,
        admin_user: Account,
    ) -> None:
        """Log successful user creation workflow."""
        self._log_audit_action(
            action=AuditActions.USER_CREATION_WORKFLOW_COMPLETED,
            target_id=user_account.id,
            target_type=AuditTargetTypes.USER,
            actor_id=admin_user.id,
            success=True,
            details={
                "user_email": user_account.email,
                "workspace_id": str(workspace.id),
                "subscription_id": str(subscription.id),
                "payment_id": str(payment.id),
                "billing_cycle": subscription.billing_cycle.value,
                "is_trial": subscription.is_trial,
            },
        )

    def _log_user_creation_failure(
        self, request: AdminUserCreationRequest, admin_user: Account, error_message: str
    ) -> None:
        """Log failed user creation workflow."""
        try:
            self._log_audit_action(
                action=AuditActions.USER_CREATION_WORKFLOW_FAILED,
                target_id=admin_user.id,  # Use admin ID as target since user wasn't created
                target_type=AuditTargetTypes.USER,
                actor_id=admin_user.id,
                success=False,
                details={
                    "attempted_email": request.email,
                    "error_message": error_message,
                    "plan_id": str(request.plan_id),
                    "is_trial": request.is_trial,
                    "billing_cycle": request.billing_cycle.value if request.billing_cycle else None,
                },
            )
            self.db_session.commit()  # Commit audit log even if main transaction failed
        except Exception as e:
            logger.warning(f"Failed to log user creation failure: {e}")

    def _build_response(
        self,
        user_account: Account,
        workspace: Workspace,
        subscription: Subscription,
        payment: Payment,
        plan: Plan,
        admin_user: Account,
    ) -> AdminUserCreationResponse:
        """Build the response object."""
        return AdminUserCreationResponse(
            success=True,
            message=f"Successfully created user {user_account.email} with workspace and subscription",
            user_id=user_account.id,
            workspace_id=workspace.id,
            subscription_id=subscription.id,
            payment_id=payment.id,
            user_email=user_account.email,
            user_name=user_account.name,
            user_status=user_account.status.value,
            workspace_name=workspace.name,
            workspace_role=WorkspaceRole.OWNER.value,
            plan_name=plan.name,
            billing_cycle=subscription.billing_cycle.value,
            is_trial=subscription.is_trial,
            subscription_start_date=subscription.start_date,
            subscription_end_date=subscription.end_date,
            amount_paid=payment.amount_paid,
            payment_reference=payment.reference,
            created_by_admin=admin_user.id,
            created_at=user_account.created_at,
        )
