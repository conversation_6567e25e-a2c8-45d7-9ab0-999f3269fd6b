"""
Clean Business Validators - Modern Decorator Approach

This module provides a unified, clean validation system using modern decorator patterns.
All validators are centralized, consistent, and easy to use.
"""

import re
from typing import Any

from pydantic import field_validator, model_validator


class ValidationRules:
    """Centralized validation rules and patterns."""

    # Regex patterns
    EMAIL_REGEX = re.compile(r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$")
    NAME_REGEX = re.compile(r"^[a-zA-Z0-9\s\-_.]+$")
    PLAN_CODE_REGEX = re.compile(r"^[A-Z0-9_-]+$")
    ACTION_REGEX = re.compile(r"^[a-z0-9_]+$")

    # Length constraints
    DEFAULT_MAX_LENGTH = 255
    EMAIL_MAX_LENGTH = 254
    NAME_MAX_LENGTH = 100
    PLAN_CODE_MAX_LENGTH = 64

    # Password requirements
    PASSWORD_MIN_LENGTH = 8
    PASSWORD_MAX_LENGTH = 128
    MAX_PRICE = 999999.99


class UnifiedValidator:
    """
    Core validation logic - used internally by FieldValidators.

    This class contains the actual validation implementations.
    Use FieldValidators for clean decorator interface.
    """

    @classmethod
    def validate_required_string(cls, value: Any, field_name: str) -> str:
        """Universal required string validator."""
        if value is None:
            raise ValueError(f"{field_name.title()} is required")
        if not isinstance(value, str):
            raise ValueError(f"{field_name.title()} must be a string")
        if not value.strip():
            raise ValueError(f"{field_name.title()} cannot be empty")
        return value.strip()

    @classmethod
    def validate_email(cls, value: Any) -> str:
        """Validate email with all edge cases."""
        email = cls.validate_required_string(value, "email")

        # Check for whitespace in email
        if " " in email:
            raise ValueError("Email cannot contain spaces")

        # Check for consecutive dots
        if ".." in email:
            raise ValueError("Email cannot contain consecutive dots")

        # Check for dots at start/end of local part
        if "@" in email:
            local_part = email.split("@")[0]
            if local_part.startswith(".") or local_part.endswith("."):
                raise ValueError("Email local part cannot start or end with a dot")

        if not ValidationRules.EMAIL_REGEX.match(email):
            raise ValueError("Invalid email format")

        if len(email) > ValidationRules.EMAIL_MAX_LENGTH:
            raise ValueError("Email address too long")

        return email.lower()

    @classmethod
    def validate_password(cls, value: Any) -> str:
        """Validate password with strength requirements."""
        password = cls.validate_required_string(value, "password")

        # Don't strip passwords - preserve original
        if value != value.strip():
            raise ValueError("Password cannot have leading or trailing whitespace")

        failed_requirements = []

        if len(password) < ValidationRules.PASSWORD_MIN_LENGTH:
            failed_requirements.append(f"Must be at least {ValidationRules.PASSWORD_MIN_LENGTH} characters long")

        if len(password) > ValidationRules.PASSWORD_MAX_LENGTH:
            failed_requirements.append(f"Must be no more than {ValidationRules.PASSWORD_MAX_LENGTH} characters long")

        if not re.search(r"[A-Z]", password):
            failed_requirements.append("Must contain at least one uppercase letter")

        if not re.search(r"[a-z]", password):
            failed_requirements.append("Must contain at least one lowercase letter")

        if not re.search(r"[0-9]", password):
            failed_requirements.append("Must contain at least one digit")

        if not re.search(r"[!@#$%^&*(),.?\":{}|<>]", password):
            failed_requirements.append("Must contain at least one special character")

        if failed_requirements:
            from ..exceptions import PasswordValidationError

            raise PasswordValidationError(failed_requirements)

        return password

    @classmethod
    def validate_name(cls, value: Any, field_name: str = "name", max_length: int = 255) -> str:
        """Validate name fields with length and character restrictions."""
        name = cls.validate_required_string(value, field_name)

        if len(name) > max_length:
            raise ValueError(f"{field_name.title()} must be no more than {max_length} characters long")

        # Check for potentially dangerous characters
        if any(char in name for char in ["<", ">", '"', "'"]):
            raise ValueError(f"{field_name.title()} contains invalid characters")

        return name

    @classmethod
    def validate_plan_code(cls, value: Any) -> str:
        """Validate plan code format."""
        plan_code = cls.validate_required_string(value, "plan code")
        plan_code = plan_code.upper()

        if not ValidationRules.PLAN_CODE_REGEX.match(plan_code):
            raise ValueError("Plan code can only contain uppercase letters, numbers, underscores, and hyphens")

        if len(plan_code) > ValidationRules.PLAN_CODE_MAX_LENGTH:
            raise ValueError("Plan code must be no more than 64 characters long")

        return plan_code

    @classmethod
    def validate_price(cls, value: Any, field_name: str = "price") -> float | None:
        """Validate price fields."""
        if value is None:
            return None

        try:
            price = float(value)
        except (ValueError, TypeError):
            raise ValueError(f"{field_name.title()} must be a valid number")

        if price < 0:
            raise ValueError(f"{field_name.title()} cannot be negative")

        if price > ValidationRules.MAX_PRICE:
            raise ValueError(f"{field_name.title()} cannot exceed {ValidationRules.MAX_PRICE:,.2f}")

        return round(price, 2)

    @classmethod
    def validate_action(cls, value: Any) -> str:
        """Validate audit log action format."""
        action = cls.validate_required_string(value, "action")
        action = action.lower().replace(" ", "_").replace("-", "_")

        if not ValidationRules.ACTION_REGEX.match(action):
            raise ValueError("Action can only contain lowercase letters, numbers, and underscores")

        if len(action) > 255:
            raise ValueError("Action must be no more than 255 characters long")

        return action

    @classmethod
    def validate_optional_text(cls, value: Any, field_name: str, max_length: int = 1000) -> str | None:
        """Validate optional text fields."""
        if value is None:
            return None

        if isinstance(value, str):
            text = value.strip()
            if len(text) == 0:
                return None  # Empty string becomes None
            if len(text) > max_length:
                raise ValueError(f"{field_name.title()} must be no more than {max_length} characters long")
            return text

        return value


# ============================================================================
# CLEAN FIELD VALIDATORS - Modern Decorator Approach
# ============================================================================


class FieldValidators:
    """
    Clean field validators using modern decorator pattern.

    This is the recommended way to add validation to Pydantic models.
    All validators are consistent, readable, and maintainable.

    Usage:
        class MyModel(BaseModel):
            name: str
            email: str

            _validate_name = FieldValidators.name()
            _validate_email = FieldValidators.email()
    """

    @staticmethod
    def email(field_name: str = "email"):
        """Email field validator decorator."""

        def validate_email(value: Any) -> str:
            return UnifiedValidator.validate_email(value)

        return field_validator(field_name, mode="before")(validate_email)

    @staticmethod
    def name(field_name: str = "name", max_length: int = ValidationRules.NAME_MAX_LENGTH):
        """Name field validator decorator."""

        def validate_name(value: Any) -> str:
            return UnifiedValidator.validate_name(value, field_name, max_length)

        return field_validator(field_name, mode="before")(validate_name)

    @staticmethod
    def plan_code(field_name: str = "plan_code"):
        """Plan code field validator decorator."""

        def validate_plan_code(value: Any) -> str:
            return UnifiedValidator.validate_plan_code(value)

        return field_validator(field_name, mode="before")(validate_plan_code)

    @staticmethod
    def password(field_name: str = "password"):
        """Password field validator decorator."""

        def validate_password(value: Any) -> str:
            return UnifiedValidator.validate_password(value)

        return field_validator(field_name, mode="before")(validate_password)

    @staticmethod
    def price(field_name: str):
        """Price field validator decorator."""

        def validate_price(value: Any) -> float | None:
            return UnifiedValidator.validate_price(value, field_name)

        return field_validator(field_name, mode="before")(validate_price)

    @staticmethod
    def optional_text(field_name: str, max_length: int = ValidationRules.DEFAULT_MAX_LENGTH):
        """Optional text field validator decorator."""

        def validate_optional_text(value: Any) -> str | None:
            return UnifiedValidator.validate_optional_text(value, field_name, max_length)

        return field_validator(field_name, mode="before")(validate_optional_text)

    @staticmethod
    def action(field_name: str = "action"):
        """Action field validator decorator."""

        def validate_action(value: Any) -> str:
            return UnifiedValidator.validate_action(value)

        return field_validator(field_name, mode="before")(validate_action)

    @staticmethod
    def required_string(field_name: str):
        """Required string field validator decorator."""

        def validate_required_string(value: Any) -> str:
            return UnifiedValidator.validate_required_string(value, field_name)

        return field_validator(field_name, mode="before")(validate_required_string)


# ============================================================================
# MODEL-LEVEL VALIDATORS - Business Logic
# ============================================================================


def validate_pricing_consistency():
    """Model validator for pricing consistency."""

    @model_validator(mode="after")
    def _validate_pricing(cls, values):
        if hasattr(values, "price_per_month") and hasattr(values, "price_per_year"):
            monthly = getattr(values, "price_per_month", None)
            yearly = getattr(values, "price_per_year", None)

            if monthly is None and yearly is None:
                raise ValueError("At least one price (monthly or yearly) must be specified")

            if monthly is not None and yearly is not None and yearly > (monthly * 12):
                raise ValueError("Yearly price should not exceed 12 times the monthly price")
        return values

    return _validate_pricing


def validate_trial_consistency():
    """Model validator for trial settings consistency."""

    @model_validator(mode="after")
    def _validate_trial(cls, values):
        if hasattr(values, "trial_available") and hasattr(values, "trial_duration_days"):
            trial_available = getattr(values, "trial_available", False)
            trial_duration = getattr(values, "trial_duration_days", None)

            if trial_available and trial_duration is not None and trial_duration < 1:
                raise ValueError("Trial duration days must be at least 1 when trial is available")

            if trial_available and trial_duration is None:
                raise ValueError("Trial duration days must be specified when trial is available")
        return values

    return _validate_trial


def validate_date_consistency(start_field: str = "start_date", end_field: str = "end_date"):
    """Model validator for date consistency."""

    @model_validator(mode="after")
    def _validate_dates(cls, values):
        if hasattr(values, start_field) and hasattr(values, end_field):
            start_date = getattr(values, start_field, None)
            end_date = getattr(values, end_field, None)

            if start_date and end_date and end_date <= start_date:
                raise ValueError(f"{end_field.replace('_', ' ').title()} must be after {start_field.replace('_', ' ')}")
        return values

    return _validate_dates


# ============================================================================
# BACKWARD COMPATIBILITY - Legacy Function Names
# ============================================================================

# These functions provide backward compatibility for existing code
# New code should use FieldValidators.* instead


def validate_email_field(field_name: str = "email"):
    """Legacy: Use FieldValidators.email() instead."""
    return FieldValidators.email(field_name)


def validate_password_field(field_name: str = "password"):
    """Legacy: Use FieldValidators.password() instead."""
    return FieldValidators.password(field_name)


def validate_name_field(field_name: str = "name", max_length: int = 255):
    """Legacy: Use FieldValidators.name() instead."""
    return FieldValidators.name(field_name, max_length)


def validate_plan_code_field(field_name: str = "plan_code"):
    """Legacy: Use FieldValidators.plan_code() instead."""
    return FieldValidators.plan_code(field_name)


def validate_price_field(field_name: str):
    """Legacy: Use FieldValidators.price() instead."""
    return FieldValidators.price(field_name)


def validate_action_field(field_name: str = "action"):
    """Legacy: Use FieldValidators.action() instead."""
    return FieldValidators.action(field_name)


def validate_optional_text_field(field_name: str, max_length: int = 1000):
    """Legacy: Use FieldValidators.optional_text() instead."""
    return FieldValidators.optional_text(field_name, max_length)


def validate_required_string_field(field_name: str):
    """Legacy: Use FieldValidators.required_string() instead."""
    return FieldValidators.required_string(field_name)
