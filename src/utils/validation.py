"""Validation utilities and custom validators.

⚠️  DEPRECATED: This module is deprecated in favor of the unified validation system.
    Use `src.utils.business_validators.UnifiedValidator` for new code.

    This module is kept for backward compatibility only.
"""

import re
import warnings
from typing import Any
from uuid import UUID

from pydantic import field_validator

from ..exceptions import (
    EmailValidationError,
    InvalidFieldValueError,
    PasswordValidationError,
)


class ValidationUtils:
    """
    Utility class for common validation operations.

    ⚠️  DEPRECATED: Use `UnifiedValidator` from `business_validators.py` instead.
        This class is kept for backward compatibility only.
    """

    # Email validation regex (basic pattern)
    EMAIL_REGEX = re.compile(r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$")

    # Password requirements
    PASSWORD_MIN_LENGTH = 8
    PASSWORD_MAX_LENGTH = 128

    @classmethod
    def validate_email(cls, email: str) -> str:
        """
        Validate email format.

        ⚠️  DEPRECATED: Use `UnifiedValidator.validate_email()` instead.

        Args:
            email: Email address to validate

        Returns:
            Validated email address

        Raises:
            EmailValidationError: If email format is invalid
        """
        warnings.warn(
            "ValidationUtils.validate_email() is deprecated. Use UnifiedValidator.validate_email() instead.",
            DeprecationWarning,
            stacklevel=2,
        )
        if not email:
            raise EmailValidationError(email, "Email cannot be empty")

        # Strip whitespace first
        email = email.strip()

        # Check for whitespace in email
        if " " in email:
            raise EmailValidationError(email, "Email cannot contain spaces")

        # Check for consecutive dots
        if ".." in email:
            raise EmailValidationError(email, "Email cannot contain consecutive dots")

        # Check for dots at start/end of local part
        if "@" in email:
            local_part = email.split("@")[0]
            if local_part.startswith(".") or local_part.endswith("."):
                raise EmailValidationError(email, "Email local part cannot start or end with a dot")

        if not cls.EMAIL_REGEX.match(email):
            raise EmailValidationError(email, "Invalid email format")

        if len(email) > 254:  # RFC 5321 limit
            raise EmailValidationError(email, "Email address too long")

        return email.lower().strip()

    @classmethod
    def validate_password(cls, password: str) -> str:
        """
        Validate password strength.

        ⚠️  DEPRECATED: Use `UnifiedValidator.validate_password()` instead.

        Args:
            password: Password to validate

        Returns:
            Validated password

        Raises:
            PasswordValidationError: If password doesn't meet requirements
        """
        warnings.warn(
            "ValidationUtils.validate_password() is deprecated. Use UnifiedValidator.validate_password() instead.",
            DeprecationWarning,
            stacklevel=2,
        )
        if not password:
            raise PasswordValidationError(["Password cannot be empty"])

        # Strip whitespace but preserve the original password
        if password != password.strip():
            raise PasswordValidationError(["Password cannot have leading or trailing whitespace"])

        failed_requirements = []

        if len(password) < cls.PASSWORD_MIN_LENGTH:
            failed_requirements.append(f"Must be at least {cls.PASSWORD_MIN_LENGTH} characters long")

        if len(password) > cls.PASSWORD_MAX_LENGTH:
            failed_requirements.append(f"Must be no more than {cls.PASSWORD_MAX_LENGTH} characters long")

        if not re.search(r"[A-Z]", password):
            failed_requirements.append("Must contain at least one uppercase letter")

        if not re.search(r"[a-z]", password):
            failed_requirements.append("Must contain at least one lowercase letter")

        if not re.search(r"\d", password):
            failed_requirements.append("Must contain at least one digit")

        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            failed_requirements.append("Must contain at least one special character")

        if failed_requirements:
            raise PasswordValidationError(failed_requirements)

        return password

    @classmethod
    def validate_uuid(cls, value: Any, field_name: str = "id") -> UUID:
        """
        Validate UUID format.

        Args:
            value: Value to validate as UUID
            field_name: Name of the field being validated

        Returns:
            Validated UUID

        Raises:
            InvalidFieldValueError: If value is not a valid UUID
        """
        if isinstance(value, UUID):
            return value

        if isinstance(value, str):
            try:
                return UUID(value)
            except ValueError:
                raise InvalidFieldValueError(
                    field=field_name,
                    value=value,
                    pattern="Valid UUID format (e.g., 123e4567-e89b-12d3-a456-************)",
                )

        raise InvalidFieldValueError(field=field_name, value=value, pattern="Valid UUID string or UUID object")

    @classmethod
    def validate_string_length(
        cls,
        value: str,
        field_name: str,
        min_length: int | None = None,
        max_length: int | None = None,
    ) -> str:
        """
        Validate string length constraints.

        Args:
            value: String value to validate
            field_name: Name of the field being validated
            min_length: Minimum allowed length
            max_length: Maximum allowed length

        Returns:
            Validated string

        Raises:
            InvalidFieldValueError: If string length is invalid
        """
        if not isinstance(value, str):
            raise InvalidFieldValueError(field=field_name, value=value, pattern="String value")

        length = len(value)

        if min_length is not None and length < min_length:
            raise InvalidFieldValueError(field=field_name, value=value, pattern=f"At least {min_length} characters")

        if max_length is not None and length > max_length:
            raise InvalidFieldValueError(field=field_name, value=value, pattern=f"At most {max_length} characters")

        return value.strip()

    @classmethod
    def validate_choice(
        cls,
        value: Any,
        field_name: str,
        allowed_values: list[Any],
    ) -> Any:
        """
        Validate that value is in allowed choices.

        Args:
            value: Value to validate
            field_name: Name of the field being validated
            allowed_values: List of allowed values

        Returns:
            Validated value

        Raises:
            InvalidFieldValueError: If value is not in allowed choices
        """
        if value not in allowed_values:
            raise InvalidFieldValueError(field=field_name, value=value, allowed_values=allowed_values)

        return value

    @classmethod
    def validate_plan_code(cls, plan_code: str) -> str:
        """
        Validate plan code format.

        Args:
            plan_code: Plan code to validate

        Returns:
            Validated plan code (uppercase)

        Raises:
            InvalidFieldValueError: If plan code format is invalid
        """
        if not plan_code or not plan_code.strip():
            raise InvalidFieldValueError(
                field="plan_code",
                value=plan_code,
                pattern="Plan code cannot be empty",
            )

        plan_code = plan_code.strip().upper()

        # Plan code should be alphanumeric with underscores/hyphens only
        if not re.match(r"^[A-Z0-9_-]+$", plan_code):
            raise InvalidFieldValueError(
                field="plan_code",
                value=plan_code,
                pattern="Plan code can only contain uppercase letters, numbers, underscores, and hyphens",
            )

        if len(plan_code) < 1:
            raise InvalidFieldValueError(
                field="plan_code",
                value=plan_code,
                pattern="Plan code must be at least 1 character long",
            )

        if len(plan_code) > 64:
            raise InvalidFieldValueError(
                field="plan_code",
                value=plan_code,
                pattern="Plan code must be no more than 64 characters long",
            )

        return plan_code

    @classmethod
    def validate_name_field(cls, name: str, field_name: str = "name", max_length: int = 255) -> str:
        """
        Validate name fields (user name, plan name, etc.).

        Args:
            name: Name to validate
            field_name: Name of the field being validated
            max_length: Maximum allowed length

        Returns:
            Validated name

        Raises:
            InvalidFieldValueError: If name is invalid
        """
        if not name or not name.strip():
            raise InvalidFieldValueError(
                field=field_name,
                value=name,
                pattern=f"{field_name.title()} cannot be empty",
            )

        name = name.strip()

        if len(name) < 1:
            raise InvalidFieldValueError(
                field=field_name,
                value=name,
                pattern=f"{field_name.title()} must be at least 1 character long",
            )

        if len(name) > max_length:
            raise InvalidFieldValueError(
                field=field_name,
                value=name,
                pattern=f"{field_name.title()} must be no more than {max_length} characters long",
            )

        # Check for potentially dangerous characters
        if any(char in name for char in ["<", ">", '"', "'"]):
            raise InvalidFieldValueError(
                field=field_name,
                value=name,
                pattern=f"{field_name.title()} contains invalid characters",
            )

        return name

    @classmethod
    def validate_price(cls, price: float | None, field_name: str = "price") -> float | None:
        """
        Validate price fields.

        Args:
            price: Price to validate
            field_name: Name of the field being validated

        Returns:
            Validated price (rounded to 2 decimal places)

        Raises:
            InvalidFieldValueError: If price is invalid
        """
        if price is None:
            return None

        try:
            price = float(price)
        except (ValueError, TypeError):
            raise InvalidFieldValueError(
                field=field_name,
                value=str(price),
                pattern=f"{field_name.title()} must be a valid number",
            )

        if price < 0:
            raise InvalidFieldValueError(
                field=field_name,
                value=str(price),
                pattern=f"{field_name.title()} cannot be negative",
            )

        if price > 999999.99:  # Reasonable upper limit
            raise InvalidFieldValueError(
                field=field_name,
                value=str(price),
                pattern=f"{field_name.title()} cannot exceed 999,999.99",
            )

        # Round to 2 decimal places for currency
        return round(price, 2)


# Backward compatibility function for validate_password_strength
def validate_password_strength(password: str) -> tuple[bool, str]:
    """
    Validate password strength (returns tuple for backward compatibility).

    Args:
        password: Password to validate

    Returns:
        Tuple of (is_valid, error_message)
    """
    try:
        ValidationUtils.validate_password(password)
        return True, ""
    except Exception as e:
        return False, str(e)


def create_pydantic_validators():
    """
    Create Pydantic validators using the validation utilities.

    Returns:
        Dictionary of validator functions that can be used in Pydantic models
    """

    @field_validator("email", mode="before")
    def validate_email_field(cls, v):
        """Pydantic validator for email fields."""
        if v is None:
            return v
        return ValidationUtils.validate_email(v)

    @field_validator("password", mode="before")
    def validate_password_field(cls, v):
        """Pydantic validator for password fields."""
        if v is None:
            return v
        return ValidationUtils.validate_password(v)

    def validate_uuid_field(field_name: str = "id"):
        """Create a UUID validator for a specific field."""

        @field_validator(field_name, mode="before")
        def _validate_uuid(cls, v):
            if v is None:
                return v
            return ValidationUtils.validate_uuid(v, field_name)

        return _validate_uuid

    def validate_string_length_field(
        field_name: str,
        min_length: int | None = None,
        max_length: int | None = None,
    ):
        """Create a string length validator for a specific field."""

        @field_validator(field_name, mode="before")
        def _validate_string_length(cls, v):
            if v is None:
                return v
            return ValidationUtils.validate_string_length(v, field_name, min_length, max_length)

        return _validate_string_length

    def validate_choice_field(field_name: str, allowed_values: list[Any]):
        """Create a choice validator for a specific field."""

        @field_validator(field_name, mode="before")
        def _validate_choice(cls, v):
            if v is None:
                return v
            return ValidationUtils.validate_choice(v, field_name, allowed_values)

        return _validate_choice

    return {
        "email": validate_email_field,
        "password": validate_password_field,
        "uuid": validate_uuid_field,
        "string_length": validate_string_length_field,
        "choice": validate_choice_field,
    }
