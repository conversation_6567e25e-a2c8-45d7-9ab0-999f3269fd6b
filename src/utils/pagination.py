"""
Reusable pagination service for FastAPI Madcrow project.

This module provides pagination service logic and utilities.
All Pydantic models are imported from src.models.pagination to eliminate duplication.
"""

import logging
import os
from typing import Annotated, Any, TypeVar

from fastapi import Depends, Query
from sqlmodel import Session, SQLModel, func, select

from ..models.pagination import (
    PaginationConfig,
    PaginationMeta,
)

# Generic type for paginated data
T = TypeVar("T", bound=SQLModel)

log = logging.getLogger(__name__)


class PaginationService:
    """Service for handling pagination operations."""

    def __init__(self, db_session: Session | None = None):
        """
        Initialize pagination service.

        Args:
            db_session: Database session (optional, can be injected later)
        """
        self.db_session = db_session
        # Load configuration from environment variables
        self.config = PaginationConfig.from_env()

        # Predefined configurations for different use cases
        self.standard_config = PaginationConfig.from_env("PAGINATION_")
        self.large_dataset_config = PaginationConfig(
            default_page_size=int(os.getenv("PAGINATION_LARGE_DATASET_PAGE_SIZE", 50)),
            max_page_size=int(os.getenv("PAGINATION_LARGE_DATASET_MAX_PAGE_SIZE", 500)),
            min_page_size=1,
        )
        self.small_dataset_config = PaginationConfig(
            default_page_size=int(os.getenv("PAGINATION_SMALL_DATASET_PAGE_SIZE", 10)),
            max_page_size=int(os.getenv("PAGINATION_SMALL_DATASET_MAX_PAGE_SIZE", 50)),
            min_page_size=1,
        )
        self.mobile_config = PaginationConfig(
            default_page_size=int(os.getenv("PAGINATION_MOBILE_PAGE_SIZE", 10)),
            max_page_size=int(os.getenv("PAGINATION_MOBILE_MAX_PAGE_SIZE", 25)),
            min_page_size=1,
        )

    def get_config(self, config_type: str = "standard") -> PaginationConfig:
        """
        Get pagination configuration by type.

        Args:
            config_type: Type of configuration (standard, large_dataset, small_dataset, mobile)

        Returns:
            PaginationConfig: The requested configuration
        """
        config_map = {
            "standard": self.standard_config,
            "large_dataset": self.large_dataset_config,
            "small_dataset": self.small_dataset_config,
            "mobile": self.mobile_config,
        }
        return config_map.get(config_type, self.standard_config)

    def validate_params(self, page: int, page_size: int, config_type: str = "standard") -> tuple[int, int]:
        """
        Validate and normalize pagination parameters.

        Args:
            page: Page number
            page_size: Page size
            config_type: Configuration type to use for validation

        Returns:
            tuple: (validated_page, validated_page_size)
        """
        config = self.get_config(config_type)

        if page < 1:
            page = 1

        if page_size < config.min_page_size:
            page_size = config.min_page_size
        elif page_size > config.max_page_size:
            page_size = config.max_page_size

        return page, page_size

    def paginate_query(
        self,
        query: Any,
        page: int,
        page_size: int,
        db_session: Session | None = None,
        config_type: str = "standard",
    ) -> tuple[list[T], PaginationMeta]:
        """
        Apply pagination to a SQLModel query.

        Args:
            query: SQLModel select query
            page: Page number (1-based)
            page_size: Number of items per page
            db_session: Database session (uses injected session if not provided)
            config_type: Configuration type to use

        Returns:
            tuple: (items, pagination_meta)
        """
        # Use provided session or injected session
        session = db_session or self.db_session
        if not session:
            raise ValueError("Database session is required")

        # Validate parameters
        page, page_size = self.validate_params(page, page_size, config_type)

        # Get total count using a simple approach
        count_query = select(func.count()).select_from(query.subquery())
        total_items = session.exec(count_query).one()

        # Apply pagination to main query
        offset = (page - 1) * page_size
        paginated_query = query.offset(offset).limit(page_size)

        # Execute query
        items = list(session.exec(paginated_query).all())

        # Create pagination metadata
        pagination_meta = PaginationMeta.create(page, page_size, total_items)

        return items, pagination_meta


# Global pagination service instance
_pagination_service = None


def get_pagination_service(db_session: Session | None = None) -> PaginationService:
    """
    Get global pagination service instance.

    Args:
        db_session: Database session (optional)

    Returns:
        PaginationService: Global pagination service instance
    """
    global _pagination_service
    if _pagination_service is None:
        _pagination_service = PaginationService(db_session)
    elif db_session and not _pagination_service.db_session:
        _pagination_service.db_session = db_session
    return _pagination_service


# Type alias for dependency injection
PaginationServiceDep = Annotated[PaginationService, Depends(get_pagination_service)]


# Convenience functions for common pagination patterns
def paginate_sqlmodel_query(
    db_session: Session,
    query: Any,
    page: int,
    page_size: int,
    config_type: str = "standard",
) -> tuple[list[T], PaginationMeta]:
    """
    Convenience function to paginate a SQLModel query.

    Args:
        db_session: Database session
        query: SQLModel select query
        page: Page number
        page_size: Page size
        config_type: Configuration type (standard, large_dataset, small_dataset, mobile)

    Returns:
        tuple: (items, pagination_meta)
    """
    service = get_pagination_service(db_session)
    return service.paginate_query(query, page, page_size, db_session, config_type)


# Query parameter helpers for FastAPI routes
def get_pagination_params(
    page: int = Query(1, ge=1, description="Page number (1-based)"),
    page_size: int = Query(20, ge=1, le=100, description="Number of items per page"),
) -> tuple[int, int]:
    """
    Get pagination parameters for FastAPI routes.

    Args:
        page: Page number from query parameter
        page_size: Page size from query parameter

    Returns:
        tuple: (page, page_size)
    """
    return page, page_size


# Type alias for pagination parameters dependency
PaginationParamsDep = Annotated[tuple[int, int], Depends(get_pagination_params)]
