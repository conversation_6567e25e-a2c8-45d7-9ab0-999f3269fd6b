"""Plan dependencies for FastAPI dependency injection."""

from typing import Annotated

from fastapi import Depends
from sqlmodel import Session

from ..services.plan_service import PlanService, get_plan_service
from .db import get_session


def get_plan_service_dep(db_session: Session = Depends(get_session)) -> PlanService:
    """
    Get plan service for dependency injection.

    Args:
        db_session: Database session

    Returns:
        PlanService: Plan service instance
    """
    return get_plan_service(db_session)


# Type annotation for dependency injection
PlanServiceDep = Annotated[PlanService, Depends(get_plan_service_dep)]
