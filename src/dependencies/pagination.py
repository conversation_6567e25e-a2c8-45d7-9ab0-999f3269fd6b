"""
FastAPI dependencies for pagination.

This module provides simple FastAPI dependencies for pagination,
following the same pattern as the health service.
"""

from typing import Annotated

from fastapi import Depends, Query

from src.utils.pagination import (
    PaginationService,
    get_pagination_params,
    get_pagination_service,
)

# Simple dependency aliases following the health service pattern
PaginationServiceDep = Annotated[PaginationService, Depends(get_pagination_service)]
PaginationParamsDep = Annotated[tuple[int, int], Depends(get_pagination_params)]


# Specific pagination dependencies for different use cases
def get_standard_pagination_params(
    page: int = Query(1, ge=1, description="Page number (1-based)"),
    page_size: int = Query(20, ge=1, le=100, description="Number of items per page"),
) -> tuple[int, int]:
    """Standard pagination parameters (20 items, max 100)."""
    return page, page_size


def get_large_dataset_pagination_params(
    page: int = Query(1, ge=1, description="Page number (1-based)"),
    page_size: int = Query(50, ge=1, le=500, description="Number of items per page"),
) -> tuple[int, int]:
    """Large dataset pagination parameters (50 items, max 500)."""
    return page, page_size


def get_small_dataset_pagination_params(
    page: int = Query(1, ge=1, description="Page number (1-based)"),
    page_size: int = Query(10, ge=1, le=50, description="Number of items per page"),
) -> tuple[int, int]:
    """Small dataset pagination parameters (10 items, max 50)."""
    return page, page_size


def get_mobile_pagination_params(
    page: int = Query(1, ge=1, description="Page number (1-based)"),
    page_size: int = Query(10, ge=1, le=25, description="Number of items per page"),
) -> tuple[int, int]:
    """Mobile pagination parameters (10 items, max 25)."""
    return page, page_size


# Type aliases for different pagination types
StandardPaginationDep = Annotated[tuple[int, int], Depends(get_standard_pagination_params)]
LargeDatasetPaginationDep = Annotated[tuple[int, int], Depends(get_large_dataset_pagination_params)]
SmallDatasetPaginationDep = Annotated[tuple[int, int], Depends(get_small_dataset_pagination_params)]
MobilePaginationDep = Annotated[tuple[int, int], Depends(get_mobile_pagination_params)]
