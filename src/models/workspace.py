"""Workspace models for workspace management."""

from datetime import datetime
from uuid import UUID

from pydantic import BaseModel, Field, model_validator

from ..entities.status import WorkspaceRole, WorkspaceStatus
from ..utils.business_validators import validate_name_field


class WorkspaceCreateRequest(BaseModel):
    """Request model for creating a new workspace."""

    name: str = Field(..., description="Workspace name")
    owner_id: UUID | None = Field(None, description="Owner account ID (optional, defaults to current user)")

    # Apply universal validators - just one line!
    _validate_name = validate_name_field("name", 255)


class WorkspaceUpdateRequest(BaseModel):
    """Request model for updating an existing workspace."""

    name: str | None = Field(None, description="Workspace name")
    status: WorkspaceStatus | None = Field(None, description="Workspace status")

    # Apply universal validators - just one line!
    _validate_name = validate_name_field("name", 255)


class WorkspaceResponse(BaseModel):
    """Response model for workspace data."""

    model_config = {"from_attributes": True}

    id: UUID = Field(..., description="Workspace unique identifier")
    name: str = Field(..., description="Workspace name")
    status: WorkspaceStatus = Field(..., description="Workspace status")
    owner_id: UUID | None = Field(None, description="Owner account ID")
    is_deleted: bool = Field(..., description="Whether workspace is deleted")
    created_at: datetime = Field(..., description="Workspace creation timestamp")
    updated_at: datetime = Field(..., description="Workspace last update timestamp")
    created_by: UUID | None = Field(None, description="ID of user who created the workspace")


class WorkspaceMemberCreateRequest(BaseModel):
    """Request model for adding a member to a workspace."""

    workspace_id: UUID = Field(..., description="Workspace ID")
    account_id: UUID = Field(..., description="Account ID to add as member")
    role: WorkspaceRole = Field(default=WorkspaceRole.USER, description="Member role")

    @model_validator(mode="after")
    def validate_member_data(self):
        """Validate member data consistency."""
        # Ensure workspace_id and account_id are different (can't add workspace as member of itself)
        if self.workspace_id == self.account_id:
            raise ValueError("Cannot add workspace as member of itself")
        return self


class WorkspaceMemberUpdateRequest(BaseModel):
    """Request model for updating workspace member role."""

    role: WorkspaceRole = Field(..., description="New member role")


class WorkspaceMemberResponse(BaseModel):
    """Response model for workspace member data."""

    model_config = {"from_attributes": True}

    id: UUID = Field(..., description="Membership unique identifier")
    workspace_id: UUID = Field(..., description="Workspace ID")
    account_id: UUID = Field(..., description="Account ID")
    role: WorkspaceRole = Field(..., description="Member role")
    invited_by: UUID | None = Field(None, description="ID of user who invited this member")
    created_at: datetime = Field(..., description="Membership creation timestamp")
    updated_at: datetime = Field(..., description="Membership last update timestamp")


class WorkspaceListResponse(BaseModel):
    """Response model for workspace list."""

    workspaces: list[WorkspaceResponse] = Field(..., description="List of workspaces")
    total: int = Field(..., description="Total number of workspaces")
    page: int = Field(..., description="Current page number")
    page_size: int = Field(..., description="Number of workspaces per page")
    total_pages: int = Field(..., description="Total number of pages")


class WorkspaceMemberListResponse(BaseModel):
    """Response model for workspace member list."""

    members: list[WorkspaceMemberResponse] = Field(..., description="List of workspace members")
    total: int = Field(..., description="Total number of members")
    page: int = Field(..., description="Current page number")
    page_size: int = Field(..., description="Number of members per page")
    total_pages: int = Field(..., description="Total number of pages")


class WorkspaceCreateResponse(BaseModel):
    """Response model for workspace creation."""

    result: str = Field(default="success", description="Result status")
    data: WorkspaceResponse = Field(..., description="Created workspace data")
    message: str = Field(default="Workspace created successfully", description="Success message")


class WorkspaceUpdateResponse(BaseModel):
    """Response model for workspace update."""

    result: str = Field(default="success", description="Result status")
    data: WorkspaceResponse = Field(..., description="Updated workspace data")
    message: str = Field(default="Workspace updated successfully", description="Success message")


class WorkspaceDeleteResponse(BaseModel):
    """Response model for workspace deletion."""

    result: str = Field(default="success", description="Result status")
    deleted_workspace_id: str = Field(..., description="ID of the deleted workspace")
    message: str = Field(default="Workspace deleted successfully", description="Success message")


class WorkspaceMemberCreateResponse(BaseModel):
    """Response model for workspace member creation."""

    result: str = Field(default="success", description="Result status")
    data: WorkspaceMemberResponse = Field(..., description="Created member data")
    message: str = Field(default="Member added successfully", description="Success message")


class WorkspaceMemberUpdateResponse(BaseModel):
    """Response model for workspace member update."""

    result: str = Field(default="success", description="Result status")
    data: WorkspaceMemberResponse = Field(..., description="Updated member data")
    message: str = Field(default="Member updated successfully", description="Success message")


class WorkspaceMemberDeleteResponse(BaseModel):
    """Response model for workspace member removal."""

    result: str = Field(default="success", description="Result status")
    removed_member_id: str = Field(..., description="ID of the removed member")
    message: str = Field(default="Member removed successfully", description="Success message")
