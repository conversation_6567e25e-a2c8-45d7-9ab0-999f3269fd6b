"""Models for admin workspace management."""

from datetime import date
from decimal import Decimal
from uuid import UUID

from pydantic import BaseModel, Field, model_validator

from ..entities.status import BillingCycle, SubscriptionStatus, WorkspaceStatus
from ..utils.business_validators import FieldValidators


class WorkspaceListItem(BaseModel):
    """Workspace item for admin list view."""

    model_config = {"from_attributes": True}

    # Workspace details
    workspace_id: UUID = Field(..., description="Workspace ID")
    workspace_name: str = Field(..., description="Workspace name")
    workspace_status: WorkspaceStatus = Field(..., description="Workspace status")
    created_at: str = Field(..., description="Workspace creation date")

    # Owner details
    owner_id: UUID | None = Field(None, description="Owner user ID")
    owner_name: str | None = Field(None, description="Owner name")
    owner_email: str | None = Field(None, description="Owner email")

    # Subscription details
    subscription_id: UUID | None = Field(None, description="Current subscription ID")
    plan_name: str | None = Field(None, description="Current plan name")
    billing_cycle: BillingCycle | None = Field(None, description="Billing cycle")
    subscription_status: SubscriptionStatus | None = Field(None, description="Subscription status")
    is_trial: bool | None = Field(None, description="Whether subscription is trial")
    subscription_end_date: date | None = Field(None, description="Subscription end date")

    # Metrics
    days_until_expiry: int | None = Field(None, description="Days until subscription expires")
    total_members: int = Field(0, description="Total workspace members")


class AdminWorkspaceListResponse(BaseModel):
    """Response for admin workspace list."""

    success: bool = Field(True, description="Whether the operation was successful")
    total_count: int = Field(..., description="Total number of workspaces")
    active_count: int = Field(..., description="Number of active workspaces")
    trial_count: int = Field(..., description="Number of trial subscriptions")
    expired_count: int = Field(..., description="Number of expired subscriptions")
    workspaces: list[WorkspaceListItem] = Field(..., description="List of workspaces")


class WorkspaceDetailsResponse(BaseModel):
    """Detailed workspace information for admin."""

    model_config = {"from_attributes": True}

    # Workspace information
    workspace_id: UUID = Field(..., description="Workspace ID")
    workspace_name: str = Field(..., description="Workspace name")
    workspace_status: WorkspaceStatus = Field(..., description="Workspace status")
    created_at: str = Field(..., description="Workspace creation date")
    updated_at: str = Field(..., description="Last update date")

    # Owner information
    owner_id: UUID | None = Field(None, description="Owner user ID")
    owner_name: str | None = Field(None, description="Owner name")
    owner_email: str | None = Field(None, description="Owner email")
    owner_status: str | None = Field(None, description="Owner account status")

    # Current subscription
    subscription: dict | None = Field(None, description="Current subscription details")

    # Members
    members: list[dict] = Field(default_factory=list, description="Workspace members")

    # Payment history
    payment_history: list[dict] = Field(default_factory=list, description="Payment history")

    # Activity logs
    recent_activity: list[dict] = Field(default_factory=list, description="Recent workspace activity")


class AdminSubscriptionUpdateRequest(BaseModel):
    """
    Simplified request for admin subscription updates.

    Supports multiple subscription management actions with flexible parameters.

    Example:
        {
          "action": "upgrade_plan",
          "plan_id": "premium-plan-uuid",
          "billing_cycle": "yearly",
          "remarks": "Upgrade to premium yearly"
        }
    """

    # Action type
    action: str = Field(
        ..., description="Action to perform: 'renew', 'upgrade_plan', 'trial_to_paid', 'change_billing'"
    )

    # Optional parameters based on action
    plan_id: UUID | None = Field(default=None, description="New plan ID (required for upgrade_plan action)")
    billing_cycle: BillingCycle | None = Field(
        default=None, description="Billing cycle (required for trial_to_paid and change_billing actions)"
    )
    extend_months: int | None = Field(
        default=1, description="Number of months to extend (for renew action)", ge=1, le=24
    )

    # Payment details (optional - auto-calculated if not provided)
    payment_amount: Decimal | None = Field(
        default=None, description="Payment amount (auto-calculated if not provided)", ge=0
    )
    payment_reference: str | None = Field(
        default=None, description="Payment reference (auto-generated if not provided)"
    )
    remarks: str | None = Field(default=None, description="Admin remarks for this action")

    # Apply field validators
    _validate_remarks = FieldValidators.optional_text("remarks", 500)

    @model_validator(mode="after")
    def validate_action_requirements(self):
        """Validate required fields based on action type."""
        valid_actions = ["renew", "upgrade_plan", "trial_to_paid", "change_billing"]

        if self.action not in valid_actions:
            raise ValueError(f"Invalid action. Must be one of: {valid_actions}")

        if self.action == "upgrade_plan" and not self.plan_id:
            raise ValueError("plan_id is required for upgrade_plan action")

        if self.action in ["trial_to_paid", "change_billing"] and not self.billing_cycle:
            raise ValueError(f"billing_cycle is required for {self.action} action")

        if self.billing_cycle and self.billing_cycle not in [BillingCycle.MONTHLY, BillingCycle.YEARLY]:
            raise ValueError("billing_cycle must be 'monthly' or 'yearly'")

        return self


class AdminSubscriptionCancelRequest(BaseModel):
    """Request for admin to cancel workspace subscription."""

    cancel_immediately: bool = Field(
        default=False, description="Whether to cancel immediately or at end of current period"
    )
    cancellation_reason: str = Field(..., description="Reason for cancellation (required for admin records)")
    refund_amount: Decimal | None = Field(default=None, description="Refund amount if applicable", ge=0)
    refund_reference: str | None = Field(default=None, description="Refund reference if applicable")

    # Apply field validators
    _validate_cancellation_reason = FieldValidators.required_string("cancellation_reason")
    _validate_refund_reference = FieldValidators.optional_text("refund_reference", 255)


class WorkspaceStatusUpdateRequest(BaseModel):
    """Request for admin to update workspace status."""

    status: WorkspaceStatus = Field(..., description="New workspace status: 'active' or 'archived'")
    reason: str = Field(..., description="Reason for status change (required for admin records)")

    # Apply field validators
    _validate_reason = FieldValidators.required_string("reason")


class AdminWorkspaceActionResponse(BaseModel):
    """Response for admin workspace actions."""

    success: bool = Field(True, description="Whether the operation was successful")
    message: str = Field(..., description="Success message describing the operation")

    # Workspace details
    workspace_id: UUID = Field(..., description="Workspace ID")
    workspace_name: str = Field(..., description="Workspace name")

    # Action details
    action_performed: str = Field(..., description="Action that was performed")
    performed_by: UUID = Field(..., description="Admin who performed the action")
    performed_at: str = Field(..., description="When the action was performed")

    # Updated subscription details (if applicable)
    subscription_details: dict | None = Field(None, description="Updated subscription information")

    # Payment details (if applicable)
    payment_details: dict | None = Field(None, description="Payment information")


class WorkspaceFilters(BaseModel):
    """Essential filters for workspace listing."""

    # Essential filters only
    status: WorkspaceStatus | None = Field(None, description="Filter by workspace status (active/archived)")

    # Pagination
    page: int = Field(1, description="Page number", ge=1)
    page_size: int = Field(50, description="Items per page", ge=1, le=100)

    # Sorting
    sort_by: str = Field("created_at", description="Sort field")
    sort_order: str = Field("desc", description="Sort order: 'asc' or 'desc'")


class WorkspaceSearchRequest(BaseModel):
    """Request for searching workspaces."""

    query: str | None = Field(None, description="Search query (workspace name, owner email, etc.)")
    filters: WorkspaceFilters = Field(default_factory=WorkspaceFilters, description="Search filters")

    # Apply field validators
    _validate_query = FieldValidators.optional_text("query", 255)
