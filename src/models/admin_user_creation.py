"""
Admin user creation models for creating users with workspace and subscription.

This module provides models for the complete admin-driven user creation workflow
that creates user accounts, workspaces, subscriptions, and payment records in a
single atomic operation.
"""

from datetime import datetime
from decimal import Decimal
from uuid import UUID

from pydantic import BaseModel, Field, model_validator

from ..entities.status import BillingCycle
from ..utils.business_validators import FieldValidators


class AdminUserCreationRequest(BaseModel):
    """
    Request model for admin user creation with complete workspace and subscription setup.

    This model supports the admin-driven user creation workflow that:
    1. Creates user account with PENDING status (no password)
    2. Auto-creates workspace (or uses custom name)
    3. Assigns user as workspace owner
    4. Creates subscription based on plan and billing cycle
    5. Records payment details
    6. Logs all actions in audit log

    Workspace Name Logic:
    - If workspace_name provided: Uses custom name
    - If workspace_name not provided: Auto-generates "{user_name}_workspace"
      Example: "John Doe" → "john_doe_workspace"

    Billing Cycle Logic:
    - "trial": Creates trial subscription using plan's trial duration
    - "monthly": Creates 30-day paid subscription
    - "yearly": Creates 365-day paid subscription

    Example Usage:
        # Auto-generated workspace
        request = AdminUserCreationRequest(
            name="<PERSON>",
            email="<EMAIL>",
            plan_id="uuid-here",
            billing_cycle="trial",
            amount_paid=0.00,
            payment_reference="TRIAL_START"
        )
        # Result: workspace_name = "john_doe_workspace"

        # Custom workspace
        request = AdminUserCreationRequest(
            name="Jane Smith",
            email="<EMAIL>",
            workspace_name="Jane's Company",
            plan_id="uuid-here",
            billing_cycle="monthly",
            amount_paid=29.99,
            payment_reference="PAYMENT_001"
        )
        # Result: workspace_name = "Jane's Company"
    """

    # User details
    name: str = Field(..., description="User's full name (used for auto-generating workspace name if not provided)")
    email: str = Field(..., description="User's email address (must be unique across all users)")

    # Workspace details (auto-generated from user name if not provided)
    workspace_name: str | None = Field(
        default=None,
        description="Optional custom workspace name. If not provided, auto-generates '{user_name}_workspace'",
    )

    # Subscription details
    plan_id: UUID = Field(..., description="ID of the subscription plan (must exist and be active)")
    is_trial: bool = Field(
        default=False, description="Whether this should be a trial subscription (uses plan's trial duration)"
    )
    billing_cycle: BillingCycle | None = Field(
        default=None, description="Billing cycle for paid subscriptions: 'monthly' or 'yearly' (required if not trial)"
    )

    # Optional admin notes
    payment_remarks: str | None = Field(
        default=None, description="Optional payment remarks or notes for admin reference"
    )

    # Apply field validators using the existing validation system
    _validate_name = FieldValidators.name("name", 255)
    _validate_email = FieldValidators.email("email")
    _validate_workspace_name = FieldValidators.optional_text("workspace_name", 255)
    _validate_payment_remarks = FieldValidators.optional_text("payment_remarks", 500)

    @model_validator(mode="after")
    def validate_subscription_logic(self):
        """Validate subscription logic: trial vs paid billing cycle."""
        if self.is_trial:
            # Trial subscriptions don't use billing cycle
            if self.billing_cycle is not None:
                raise ValueError("billing_cycle should not be provided for trial subscriptions")
        else:
            # Paid subscriptions require billing cycle
            if self.billing_cycle is None:
                raise ValueError("billing_cycle is required for paid subscriptions")
            if self.billing_cycle not in [BillingCycle.MONTHLY, BillingCycle.YEARLY]:
                raise ValueError("billing_cycle must be 'monthly' or 'yearly' for paid subscriptions")

        return self


class AdminUserCreationResponse(BaseModel):
    """
    Response model for admin user creation workflow.

    Contains complete details of all entities created during the admin user creation process:
    - User account (with PENDING status)
    - Workspace (auto-generated or custom name)
    - Workspace membership (OWNER role)
    - Subscription (trial, monthly, or yearly)
    - Payment record (trial or paid)

    This response provides all necessary information for tracking and auditing
    the complete user creation workflow.
    """

    success: bool = Field(True, description="Whether the user creation workflow was successful")
    message: str = Field(..., description="Success message describing the completed workflow")

    # Created entities (UUIDs for all created records)
    user_id: UUID = Field(..., description="ID of the created user account")
    workspace_id: UUID = Field(..., description="ID of the created workspace")
    subscription_id: UUID = Field(..., description="ID of the created subscription")
    payment_id: UUID = Field(..., description="ID of the created payment record")

    # User details (account information)
    user_email: str = Field(..., description="Email address of the created user")
    user_name: str = Field(..., description="Full name of the created user")
    user_status: str = Field(..., description="User account status (always 'pending' for new users)")

    # Workspace details (workspace and membership information)
    workspace_name: str = Field(..., description="Name of the created workspace (auto-generated or custom)")
    workspace_role: str = Field(..., description="User's role in the workspace (always 'owner')")

    # Subscription details (plan and billing information)
    plan_name: str = Field(..., description="Name of the selected subscription plan")
    billing_cycle: str = Field(..., description="Billing cycle: 'trial', 'monthly', or 'yearly'")
    is_trial: bool = Field(..., description="Whether this is a trial subscription (true for trial billing_cycle)")
    subscription_start_date: datetime = Field(..., description="Subscription start date (current date)")
    subscription_end_date: datetime = Field(
        ..., description="Subscription end date (based on billing cycle or trial duration)"
    )

    # Payment details (payment record information)
    amount_paid: Decimal = Field(..., description="Amount paid for the subscription (0 for trials)")
    payment_reference: str = Field(..., description="Payment reference or transaction ID")

    # Audit information (tracking and accountability)
    created_by_admin: UUID = Field(..., description="ID of the admin user who created this user")
    created_at: datetime = Field(..., description="Timestamp when the user was created")


class AdminUserCreationSummary(BaseModel):
    """Summary model for listing admin-created users."""

    user_id: UUID = Field(..., description="User ID")
    user_name: str = Field(..., description="User name")
    user_email: str = Field(..., description="User email")
    user_status: str = Field(..., description="User status")

    workspace_id: UUID = Field(..., description="Workspace ID")
    workspace_name: str = Field(..., description="Workspace name")

    subscription_id: UUID = Field(..., description="Subscription ID")
    plan_name: str = Field(..., description="Plan name")
    billing_cycle: str = Field(..., description="Billing cycle")
    is_trial: bool = Field(..., description="Whether this is a trial")

    created_by_admin: UUID = Field(..., description="Admin who created this user")
    created_at: datetime = Field(..., description="Creation timestamp")


class AdminUserCreationStats(BaseModel):
    """Statistics for admin user creation."""

    total_users_created: int = Field(..., description="Total users created by admins")
    trial_users: int = Field(..., description="Users on trial subscriptions")
    paid_users: int = Field(..., description="Users on paid subscriptions")
    active_users: int = Field(..., description="Users with active status")
    pending_users: int = Field(..., description="Users with pending status")
    total_revenue: Decimal = Field(..., description="Total revenue from admin-created users")

    # Breakdown by billing cycle
    monthly_subscriptions: int = Field(..., description="Monthly subscriptions")
    yearly_subscriptions: int = Field(..., description="Yearly subscriptions")
    trial_subscriptions: int = Field(..., description="Trial subscriptions")

    # Recent activity
    users_created_last_30_days: int = Field(..., description="Users created in last 30 days")
    revenue_last_30_days: Decimal = Field(..., description="Revenue in last 30 days")
