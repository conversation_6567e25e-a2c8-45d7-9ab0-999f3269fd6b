"""
Pagination models for FastAPI Madcrow project.

This module provides all pagination-related Pydantic models including:
- Configuration models for pagination behavior
- Request parameter models
- Response models for paginated data
- Legacy compatibility models

All pagination models are centralized here to eliminate duplication.
"""

import os
from typing import Any, TypeVar

from fastapi import Query
from pydantic import BaseModel, ConfigDict, Field

# Generic type for paginated items
T = TypeVar("T")


class PaginationConfig(BaseModel):
    """Configuration for pagination behavior."""

    default_page_size: int = Field(default=20, ge=1, le=1000, description="Default number of items per page")
    max_page_size: int = Field(default=100, ge=1, le=1000, description="Maximum allowed page size")
    min_page_size: int = Field(default=1, ge=1, description="Minimum allowed page size")
    default_page: int = Field(default=1, ge=1, description="Default page number")

    @classmethod
    def from_env(cls, prefix: str = "PAGINATION_") -> "PaginationConfig":
        """Create configuration from environment variables."""
        return cls(
            default_page_size=int(os.getenv(f"{prefix}DEFAULT_PAGE_SIZE", 20)),
            max_page_size=int(os.getenv(f"{prefix}MAX_PAGE_SIZE", 100)),
            min_page_size=int(os.getenv(f"{prefix}MIN_PAGE_SIZE", 1)),
            default_page=int(os.getenv(f"{prefix}DEFAULT_PAGE", 1)),
        )


class PaginationParams(BaseModel):
    """Pagination parameters for API requests."""

    page: int = Field(ge=1, description="Page number (1-based)")
    page_size: int = Field(ge=1, description="Number of items per page")

    @classmethod
    def create_query_params(
        cls,
        config: PaginationConfig | None = None,
    ) -> tuple[int, int]:
        """
        Create FastAPI Query parameters for pagination.

        Args:
            config: Pagination configuration

        Returns:
            tuple: (page Query, page_size Query) for use in FastAPI route parameters
        """
        if config is None:
            config = PaginationConfig()

        page = Query(
            config.default_page,
            ge=1,
            description="Page number (1-based)",
            example=1,
        )
        page_size = Query(
            config.default_page_size,
            ge=config.min_page_size,
            le=config.max_page_size,
            description=f"Number of items per page (max: {config.max_page_size})",
            example=config.default_page_size,
        )
        return page, page_size


class PaginationMeta(BaseModel):
    """Pagination metadata model."""

    page: int = Field(..., ge=1, description="Current page number (1-based)")
    page_size: int = Field(..., ge=1, description="Number of items per page")
    total_items: int = Field(..., ge=0, description="Total number of items across all pages")
    total_pages: int = Field(..., ge=0, description="Total number of pages")
    has_next: bool = Field(..., description="Whether there is a next page available")
    has_previous: bool = Field(..., description="Whether there is a previous page available")
    next_page: int | None = Field(None, ge=1, description="Next page number if available")
    previous_page: int | None = Field(None, ge=1, description="Previous page number if available")

    @classmethod
    def create(cls, page: int, page_size: int, total_items: int) -> "PaginationMeta":
        """
        Create pagination metadata from basic parameters.

        Args:
            page: Current page number
            page_size: Items per page
            total_items: Total number of items

        Returns:
            PaginationMeta: Complete pagination metadata
        """
        total_pages = (total_items + page_size - 1) // page_size if total_items > 0 else 0
        has_next = page < total_pages
        has_previous = page > 1

        return cls(
            page=page,
            page_size=page_size,
            total_items=total_items,
            total_pages=total_pages,
            has_next=has_next,
            has_previous=has_previous,
            next_page=page + 1 if has_next else None,
            previous_page=page - 1 if has_previous else None,
        )


# Alias for backward compatibility
PaginationInfo = PaginationMeta


class PaginatedResponse[T](BaseModel):
    """Generic paginated response model."""

    model_config = ConfigDict(arbitrary_types_allowed=True)

    items: list[T] = Field(..., description="List of items for the current page")
    pagination: PaginationMeta = Field(..., description="Pagination metadata")

    @classmethod
    def create(
        cls,
        items: list[T],
        page: int,
        page_size: int,
        total_items: int,
    ) -> "PaginatedResponse[T]":
        """
        Create a paginated response from items and pagination parameters.

        Args:
            items: List of items for current page
            page: Current page number
            page_size: Items per page
            total_items: Total number of items

        Returns:
            PaginatedResponse: Complete paginated response
        """
        pagination_meta = PaginationMeta.create(page, page_size, total_items)
        return cls(items=items, pagination=pagination_meta)


# Legacy compatibility models (for backward compatibility with existing plan endpoints)
class LegacyPaginationMixin(BaseModel):
    """Legacy pagination fields for backward compatibility."""

    total: int = Field(..., description="Total number of items (legacy field)")
    page: int = Field(..., description="Current page number (legacy field)")
    page_size: int = Field(..., description="Number of items per page (legacy field)")
    total_pages: int = Field(..., description="Total number of pages (legacy field)")


class PaginatedResponseWithLegacy[T](BaseModel):
    """Paginated response with both new and legacy pagination fields."""

    items: list[T] = Field(..., description="List of items for the current page")
    pagination: PaginationMeta = Field(..., description="Pagination metadata")

    # Legacy fields for backward compatibility
    total: int = Field(..., description="Total number of items (legacy)")
    page: int = Field(..., description="Current page number (legacy)")
    page_size: int = Field(..., description="Number of items per page (legacy)")
    total_pages: int = Field(..., description="Total number of pages (legacy)")

    @classmethod
    def create(
        cls,
        items: list[T],
        page: int,
        page_size: int,
        total_items: int,
    ) -> "PaginatedResponseWithLegacy[T]":
        """
        Create a paginated response with legacy fields.

        Args:
            items: List of items for current page
            page: Current page number
            page_size: Items per page
            total_items: Total number of items

        Returns:
            PaginatedResponseWithLegacy: Complete paginated response with legacy fields
        """
        pagination_meta = PaginationMeta.create(page, page_size, total_items)
        total_pages = pagination_meta.total_pages

        return cls(
            items=items,
            pagination=pagination_meta,
            # Legacy fields
            total=total_items,
            page=page,
            page_size=page_size,
            total_pages=total_pages,
        )


# Specific response models for common use cases
class EmptyPaginatedResponse(BaseModel):
    """Empty paginated response for endpoints with no items."""

    items: list[Any] = Field(default_factory=list, description="Empty list of items")
    pagination: PaginationMeta = Field(..., description="Pagination metadata")

    @classmethod
    def create_empty(cls, page: int = 1, page_size: int = 20) -> "EmptyPaginatedResponse":
        """
        Create an empty paginated response.

        Args:
            page: Current page number
            page_size: Items per page

        Returns:
            EmptyPaginatedResponse: Empty paginated response
        """
        pagination_meta = PaginationMeta.create(page, page_size, 0)
        return cls(items=[], pagination=pagination_meta)


# Helper functions for creating paginated responses
def create_paginated_response[T](
    items: list[T],
    page: int,
    page_size: int,
    total_items: int,
    use_legacy: bool = False,
) -> PaginatedResponse[T] | PaginatedResponseWithLegacy[T]:
    """
    Create a paginated response with optional legacy compatibility.

    Args:
        items: List of items for current page
        page: Current page number
        page_size: Items per page
        total_items: Total number of items
        use_legacy: Whether to include legacy fields

    Returns:
        PaginatedResponse or PaginatedResponseWithLegacy
    """
    if use_legacy:
        return PaginatedResponseWithLegacy.create(items, page, page_size, total_items)
    else:
        return PaginatedResponse.create(items, page, page_size, total_items)


def create_empty_paginated_response(
    page: int = 1,
    page_size: int = 20,
) -> EmptyPaginatedResponse:
    """
    Create an empty paginated response.

    Args:
        page: Current page number
        page_size: Items per page

    Returns:
        EmptyPaginatedResponse: Empty paginated response
    """
    return EmptyPaginatedResponse.create_empty(page, page_size)
