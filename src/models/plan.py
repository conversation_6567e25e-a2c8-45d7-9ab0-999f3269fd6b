"""Plan models for subscription plan management."""

from datetime import datetime
from uuid import UUID

from pydantic import BaseModel, Field

from ..utils.business_validators import (
    FieldValidators,
    validate_pricing_consistency,
    validate_trial_consistency,
)


class PlanCreateRequest(BaseModel):
    """Request model for creating a new plan."""

    name: str = Field(..., min_length=1, max_length=64, description="Plan name")
    plan_code: str = Field(..., min_length=1, max_length=64, description="Plan code - stable internal reference")
    description: str | None = Field(None, max_length=255, description="Plan description")
    price_per_month: float | None = Field(None, ge=0, description="Monthly price")
    price_per_year: float | None = Field(None, ge=0, description="Yearly price")
    is_active: bool = Field(default=True, description="Whether the plan is active")
    is_public: bool = Field(default=True, description="Whether the plan is publicly available")
    trial_available: bool = Field(default=False, description="Whether trial is available")
    trial_duration_days: int | None = Field(None, description="Trial duration in days")
    features: dict = Field(default_factory=dict, description="Plan features as JSON object")

    # Apply new clean validators - much cleaner!
    _validate_name = FieldValidators.name("name", 64)
    _validate_plan_code = FieldValidators.plan_code()
    _validate_description = FieldValidators.optional_text("description", 255)
    _validate_price_per_month = FieldValidators.price("price_per_month")
    _validate_price_per_year = FieldValidators.price("price_per_year")

    # Apply model-level validators
    _validate_trial = validate_trial_consistency()
    _validate_pricing = validate_pricing_consistency()


class PlanUpdateRequest(BaseModel):
    """Request model for updating an existing plan."""

    name: str | None = Field(None, min_length=1, max_length=64, description="Plan name")
    plan_code: str | None = Field(None, min_length=1, max_length=64, description="Plan code")
    description: str | None = Field(None, max_length=255, description="Plan description")
    price_per_month: float | None = Field(None, ge=0, description="Monthly price")
    price_per_year: float | None = Field(None, ge=0, description="Yearly price")
    is_active: bool | None = Field(None, description="Whether the plan is active")
    is_public: bool | None = Field(None, description="Whether the plan is publicly available")
    trial_available: bool | None = Field(None, description="Whether trial is available")
    trial_duration_days: int | None = Field(None, description="Trial duration in days")
    features: dict | None = Field(None, description="Plan features as JSON object")

    # Apply new clean validators - much cleaner!
    _validate_name = FieldValidators.name("name", 64)
    _validate_plan_code = FieldValidators.plan_code()
    _validate_description = FieldValidators.optional_text("description", 255)
    _validate_price_per_month = FieldValidators.price("price_per_month")
    _validate_price_per_year = FieldValidators.price("price_per_year")

    # Apply model-level validators
    _validate_trial = validate_trial_consistency()


class PlanResponse(BaseModel):
    """Response model for plan data."""

    model_config = {"from_attributes": True}

    id: UUID = Field(..., description="Plan unique identifier")
    name: str = Field(..., description="Plan name")
    plan_code: str = Field(..., description="Plan code")
    description: str | None = Field(None, description="Plan description")
    price_per_month: float | None = Field(None, description="Monthly price")
    price_per_year: float | None = Field(None, description="Yearly price")
    is_active: bool = Field(..., description="Whether the plan is active")
    is_public: bool = Field(..., description="Whether the plan is publicly available")
    trial_available: bool = Field(..., description="Whether trial is available")
    trial_duration_days: int | None = Field(None, description="Trial duration in days")
    features: dict = Field(..., description="Plan features")
    created_at: datetime = Field(..., description="Plan creation timestamp")
    updated_at: datetime = Field(..., description="Plan last update timestamp")
    created_by: UUID | None = Field(None, description="ID of user who created the plan")


class PlanListResponse(BaseModel):
    """Response model for plan list."""

    plans: list[PlanResponse] = Field(..., description="List of plans")
    total: int = Field(..., description="Total number of plans")
    page: int = Field(..., description="Current page number")
    page_size: int = Field(..., description="Number of plans per page")
    total_pages: int = Field(..., description="Total number of pages")


class PlanCreateResponse(BaseModel):
    """Response model for plan creation."""

    result: str = Field(default="success", description="Result status")
    data: PlanResponse = Field(..., description="Created plan data")
    message: str = Field(default="Plan created successfully", description="Success message")


class PlanUpdateResponse(BaseModel):
    """Response model for plan update."""

    result: str = Field(default="success", description="Result status")
    data: PlanResponse = Field(..., description="Updated plan data")
    message: str = Field(default="Plan updated successfully", description="Success message")


class PlanDeleteResponse(BaseModel):
    """Response model for plan deletion."""

    result: str = Field(default="success", description="Result status")
    deleted_plan_id: str = Field(..., description="ID of the deleted plan")
    message: str = Field(default="Plan deleted successfully", description="Success message")
