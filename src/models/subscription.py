"""Subscription models for subscription management."""

from datetime import date, datetime
from uuid import UUID

from pydantic import BaseModel, Field, field_validator, model_validator

from ..entities.status import BillingCycle, SubscriptionStatus
from ..utils.business_validators import (
    validate_date_consistency,
    validate_optional_text_field,
    validate_price_field,
    validate_required_string_field,
)


class SubscriptionCreateRequest(BaseModel):
    """Request model for creating a new subscription."""

    workspace_id: UUID = Field(..., description="Workspace ID")
    plan_id: UUID = Field(..., description="Plan ID")
    billing_cycle: BillingCycle = Field(default=BillingCycle.MONTHLY, description="Billing cycle")
    start_date: date | None = Field(None, description="Subscription start date")
    end_date: date | None = Field(None, description="Subscription end date")
    is_trial: bool = Field(default=False, description="Whether this is a trial subscription")

    # Apply universal validators
    _validate_dates = validate_date_consistency("start_date", "end_date")


class SubscriptionUpdateRequest(BaseModel):
    """Request model for updating an existing subscription."""

    billing_cycle: BillingCycle | None = Field(None, description="Billing cycle")
    start_date: date | None = Field(None, description="Subscription start date")
    end_date: date | None = Field(None, description="Subscription end date")
    status: SubscriptionStatus | None = Field(None, description="Subscription status")
    is_trial: bool | None = Field(None, description="Whether this is a trial subscription")
    is_current: bool | None = Field(None, description="Whether this is the current subscription")

    @model_validator(mode="after")
    def validate_subscription_dates(self):
        """Validate subscription date consistency."""
        if self.start_date and self.end_date and self.end_date <= self.start_date:
            raise ValueError("End date must be after start date")

        if (
            self.is_trial
            and self.billing_cycle
            and self.billing_cycle not in [BillingCycle.TRIAL, BillingCycle.MONTHLY]
        ):
            raise ValueError("Trial subscriptions should use TRIAL or MONTHLY billing cycle")

        return self


class SubscriptionResponse(BaseModel):
    """Response model for subscription data."""

    model_config = {"from_attributes": True}

    id: UUID = Field(..., description="Subscription unique identifier")
    workspace_id: UUID = Field(..., description="Workspace ID")
    plan_id: UUID = Field(..., description="Plan ID")
    billing_cycle: BillingCycle = Field(..., description="Billing cycle")
    start_date: date | None = Field(None, description="Subscription start date")
    end_date: date | None = Field(None, description="Subscription end date")
    status: SubscriptionStatus = Field(..., description="Subscription status")
    is_trial: bool = Field(..., description="Whether this is a trial subscription")
    is_current: bool = Field(..., description="Whether this is the current subscription")
    previous_subscription_id: UUID | None = Field(None, description="Previous subscription ID")
    created_at: datetime = Field(..., description="Subscription creation timestamp")
    updated_at: datetime = Field(..., description="Subscription last update timestamp")
    created_by: UUID | None = Field(None, description="ID of user who created the subscription")


class PaymentCreateRequest(BaseModel):
    """Request model for creating a new payment."""

    subscription_id: UUID = Field(..., description="Subscription ID")
    reference: str = Field(..., min_length=1, max_length=255, description="Payment reference")
    amount_paid: float = Field(..., gt=0, description="Amount paid")
    paid_at: date | None = Field(None, description="Payment date")
    remarks: str | None = Field(None, max_length=500, description="Payment remarks")

    # Apply universal validators - just one line each!
    _validate_reference = validate_required_string_field("reference")
    _validate_amount = validate_price_field("amount_paid")
    _validate_remarks = validate_optional_text_field("remarks", 500)


class PaymentUpdateRequest(BaseModel):
    """Request model for updating an existing payment."""

    paid_at: date | None = Field(None, description="Payment date")
    remarks: str | None = Field(None, max_length=500, description="Payment remarks")

    @field_validator("remarks", mode="before")
    @classmethod
    def validate_remarks_field(cls, v):
        """Validate payment remarks."""
        if v is None:
            return v

        if isinstance(v, str):
            remarks = v.strip()
            if len(remarks) == 0:
                return None  # Empty string becomes None
            if len(remarks) > 500:
                raise ValueError("Payment remarks must be no more than 500 characters long")
            return remarks

        return v


class PaymentResponse(BaseModel):
    """Response model for payment data."""

    model_config = {"from_attributes": True}

    id: UUID = Field(..., description="Payment unique identifier")
    subscription_id: UUID = Field(..., description="Subscription ID")
    paid_at: date | None = Field(None, description="Payment date")
    reference: str = Field(..., description="Payment reference")
    remarks: str | None = Field(None, description="Payment remarks")
    amount_paid: float = Field(..., description="Amount paid")
    created_at: datetime = Field(..., description="Payment creation timestamp")
    updated_at: datetime = Field(..., description="Payment last update timestamp")
    created_by: UUID | None = Field(None, description="ID of user who created the payment")


class SubscriptionListResponse(BaseModel):
    """Response model for subscription list."""

    subscriptions: list[SubscriptionResponse] = Field(..., description="List of subscriptions")
    total: int = Field(..., description="Total number of subscriptions")
    page: int = Field(..., description="Current page number")
    page_size: int = Field(..., description="Number of subscriptions per page")
    total_pages: int = Field(..., description="Total number of pages")


class PaymentListResponse(BaseModel):
    """Response model for payment list."""

    payments: list[PaymentResponse] = Field(..., description="List of payments")
    total: int = Field(..., description="Total number of payments")
    page: int = Field(..., description="Current page number")
    page_size: int = Field(..., description="Number of payments per page")
    total_pages: int = Field(..., description="Total number of pages")


class SubscriptionCreateResponse(BaseModel):
    """Response model for subscription creation."""

    result: str = Field(default="success", description="Result status")
    data: SubscriptionResponse = Field(..., description="Created subscription data")
    message: str = Field(default="Subscription created successfully", description="Success message")


class SubscriptionUpdateResponse(BaseModel):
    """Response model for subscription update."""

    result: str = Field(default="success", description="Result status")
    data: SubscriptionResponse = Field(..., description="Updated subscription data")
    message: str = Field(default="Subscription updated successfully", description="Success message")


class PaymentCreateResponse(BaseModel):
    """Response model for payment creation."""

    result: str = Field(default="success", description="Result status")
    data: PaymentResponse = Field(..., description="Created payment data")
    message: str = Field(default="Payment created successfully", description="Success message")


class PaymentUpdateResponse(BaseModel):
    """Response model for payment update."""

    result: str = Field(default="success", description="Result status")
    data: PaymentResponse = Field(..., description="Updated payment data")
    message: str = Field(default="Payment updated successfully", description="Success message")
