"""Admin user creation routes for creating users with workspace and subscription."""

import logging
from typing import Annotated

from fastapi import Depends, HTTPException, Request

from ...dependencies.auth import CurrentAdminUser
from ...dependencies.db import DatabaseSession
from ...models.admin_user_creation import (
    AdminUserCreationRequest,
    AdminUserCreationResponse,
)
from ...services.admin_user_creation_service import AdminUserCreationService
from ..base_router import BaseRouter
from ..cbv import cbv, post

logger = logging.getLogger(__name__)

admin_user_creation_router = BaseRouter(
    prefix="/v1/admin",
    tags=["admin-user-creation"],
)


def get_admin_user_creation_service(db_session: DatabaseSession) -> AdminUserCreationService:
    """Dependency to get admin user creation service."""
    return AdminUserCreationService(db_session)


AdminUserCreationServiceDep = Annotated[AdminUserCreationService, Depends(get_admin_user_creation_service)]


@cbv(admin_user_creation_router)
class AdminUserCreationController:
    """Controller for admin user creation operations."""

    @post("/users/create", operation_id="admin_create_user", response_model=AdminUserCreationResponse)
    async def create_user_with_workspace_and_subscription(
        self,
        request: AdminUserCreationRequest,
        current_admin: CurrentAdminUser,
        service: AdminUserCreationServiceDep,
        http_request: Request,
    ) -> AdminUserCreationResponse:
        """
        Create a new user with workspace and subscription (Admin only).

        This endpoint implements the complete admin-driven user creation workflow:

        1. **User Creation**: Creates a new user account with PENDING status and no password
        2. **Workspace Setup**: Auto-creates workspace ({user_name}_workspace) and assigns user as owner
        3. **Membership Assignment**: Creates workspace membership with OWNER role
        4. **Subscription Creation**: Creates subscription based on plan and trial choice
        5. **Payment Recording**: Auto-calculates payment amount and generates unique reference
        6. **Audit Logging**: Logs all actions with the admin as actor

        **Workflow Details:**
        - User account is created with `status = PENDING` and no password
        - User will need to set password later through activation process
        - Workspace is auto-generated as "{user_name}_workspace" (e.g., "john_doe_workspace")
        - If workspace_name not provided, auto-generates from user name
        - Subscription type determined by explicit `is_trial` choice (true/false)
        - For trial subscriptions: amount = 0, reference = "trial_{subscription_id}"
        - For paid subscriptions: amount = plan price, reference = "{billing_cycle}_{subscription_id}"
        - Payment amounts and references are auto-calculated/generated
        - All steps are logged in audit log with admin as actor

        **Permission Requirements:**
        - Only admin users can access this endpoint
        - Admin privileges are automatically validated

        Args:
            request: User creation request with user, workspace, and subscription details
            current_admin: Current authenticated admin user
            service: Admin user creation service
            http_request: HTTP request object for logging

        Returns:
            AdminUserCreationResponse: Complete response with all created entities

        Raises:
            HTTPException:
                - 400: If validation fails or entities already exist
                - 404: If plan not found
                - 403: If user is not admin (handled by dependency)
                - 500: If database operations fail

        Example Requests:

        **Trial Subscription (auto-generated workspace):**
        ```json
        {
            "name": "John Doe",
            "email": "<EMAIL>",
            "plan_id": "123e4567-e89b-12d3-a456-426614174000",
            "is_trial": true,
            "payment_remarks": "Trial subscription for new user"
        }
        ```

        **Paid Monthly Subscription (custom workspace name):**
        ```json
        {
            "name": "Jane Smith",
            "email": "<EMAIL>",
            "workspace_name": "Jane's Company",
            "plan_id": "123e4567-e89b-12d3-a456-426614174000",
            "is_trial": false,
            "billing_cycle": "monthly",
            "payment_remarks": "Monthly subscription for client"
        }
        ```

        **Paid Yearly Subscription (auto-generated workspace):**
        ```json
        {
            "name": "Bob Wilson",
            "email": "<EMAIL>",
            "plan_id": "123e4567-e89b-12d3-a456-426614174000",
            "is_trial": false,
            "billing_cycle": "yearly",
            "payment_remarks": "Yearly subscription with discount"
        }
        ```

        Example Response:
        ```json
        {
            "success": true,
            "message": "Successfully <NAME_EMAIL> with workspace and subscription",
            "user_id": "456e7890-e89b-12d3-a456-426614174001",
            "workspace_id": "789e0123-e89b-12d3-a456-426614174002",
            "subscription_id": "012e3456-e89b-12d3-a456-426614174003",
            "payment_id": "345e6789-e89b-12d3-a456-426614174004",
            "user_email": "<EMAIL>",
            "user_name": "John Doe",
            "user_status": "pending",
            "workspace_name": "john_doe_workspace",
            "workspace_role": "owner",
            "plan_name": "Professional Plan",
            "billing_cycle": "trial",
            "is_trial": true,
            "subscription_start_date": "2024-01-01T00:00:00Z",
            "subscription_end_date": "2024-01-15T00:00:00Z",
            "amount_paid": "0.0",
            "payment_reference": "trial_12345678",
            "created_by_admin": "678e9012-e89b-12d3-a456-426614174005",
            "created_at": "2024-01-01T00:00:00Z"
        }
        ```
        """
        try:
            logger.info(
                f"Admin {current_admin.email} creating user: {request.email} "
                f"with plan {request.plan_id}, trial: {request.is_trial}"
                + (f", billing: {request.billing_cycle}" if request.billing_cycle else "")
            )

            # Create user with complete workflow
            response = service.create_user_with_workspace_and_subscription(request, current_admin)

            logger.info(f"Successfully created user {response.user_email} by admin {current_admin.email}")

            return response

        except Exception as e:
            logger.exception(f"Failed to create user {request.email} by admin {current_admin.email}")

            # Convert service exceptions to HTTP exceptions
            if hasattr(e, "status_code"):
                raise HTTPException(status_code=e.status_code, detail=str(e))
            else:
                raise HTTPException(status_code=500, detail="Failed to create user with workspace and subscription")
