"""Admin workspace management routes."""

import logging
from typing import Annotated
from uuid import UUID

from fastapi import Depends, HTTPException, Query

from ...dependencies.auth import CurrentAdminUser
from ...dependencies.db import DatabaseSession
from ...dependencies.pagination import LargeDatasetPaginationDep
from ...models.admin_workspace_management import (
    AdminSubscriptionCancelRequest,
    AdminSubscriptionUpdateRequest,
    AdminWorkspaceActionResponse,
    AdminWorkspaceListResponse,
    WorkspaceDetailsResponse,
    WorkspaceFilters,
    WorkspaceSearchRequest,
    WorkspaceStatusUpdateRequest,
)
from ...services.admin_workspace_service import AdminWorkspaceService
from ..base_router import BaseRouter
from ..cbv import cbv, delete, get, patch, put

logger = logging.getLogger(__name__)

admin_workspace_router = BaseRouter(
    prefix="/v1/admin/workspaces",
    tags=["admin-workspace-management"],
)


# Service dependency
def get_admin_workspace_service(db: DatabaseSession) -> AdminWorkspaceService:
    """Get admin workspace service instance."""
    return AdminWorkspaceService(db)


AdminWorkspaceServiceDep = Annotated[AdminWorkspaceService, Depends(get_admin_workspace_service)]


@cbv(admin_workspace_router)
class AdminWorkspaceController:
    """Controller for admin workspace management."""

    @get("", operation_id="list_all_workspaces", response_model=AdminWorkspaceListResponse)
    async def list_all_workspaces(
        self,
        current_admin: CurrentAdminUser,
        service: AdminWorkspaceServiceDep,
        pagination: LargeDatasetPaginationDep,
        # Essential filters only
        query: str | None = Query(None, description="Search workspace name or owner email"),
        status: str | None = Query(None, description="Filter by workspace status (active/archived)"),
        # Sorting
        sort_by: str = Query("created_at", description="Sort field"),
        sort_order: str = Query("desc", description="Sort order: 'asc' or 'desc'"),
    ) -> AdminWorkspaceListResponse:
        """
        List all workspaces with subscription information (Admin only).

        This endpoint provides essential workspace management for admins:
        - View all workspaces with subscription details
        - Search by workspace name or owner email
        - Filter by essential criteria only
        - Paginated results with sorting
        - Summary statistics (active, trial, expired counts)

        Args:
            current_admin: Current authenticated admin user
            service: Admin workspace service
            pagination: Pagination parameters (page, page_size)
            query: Search workspace name or owner email
            status: Filter by workspace status (active/archived)
            sort_by: Field to sort by
            sort_order: Sort order (asc/desc)

        Returns:
            AdminWorkspaceListResponse: List of workspaces with summary statistics

        Raises:
            HTTPException:
                - 403: If user is not admin
                - 500: If database operations fail

        Example Response:
        ```json
        {
            "success": true,
            "total_count": 150,
            "active_count": 120,
            "trial_count": 25,
            "expired_count": 5,
            "workspaces": [
                {
                    "workspace_id": "uuid",
                    "workspace_name": "Acme Corp Workspace",
                    "workspace_status": "active",
                    "owner_name": "John Doe",
                    "owner_email": "<EMAIL>",
                    "plan_name": "Professional",
                    "billing_cycle": "monthly",
                    "subscription_status": "active",
                    "is_trial": false,
                    "subscription_end_date": "2024-02-01",
                    "days_until_expiry": 15,
                    "total_members": 5
                }
            ]
        }
        ```
        """
        try:
            logger.info(f"Admin {current_admin.email} listing workspaces")

            # Extract pagination parameters
            page, page_size = pagination

            # Build search request with essential filters only
            filters = WorkspaceFilters(
                status=status,
                page=page,
                page_size=page_size,
                sort_by=sort_by,
                sort_order=sort_order,
            )

            search_request = WorkspaceSearchRequest(query=query, filters=filters)

            return service.list_workspaces(search_request)

        except Exception as e:
            logger.exception(f"Failed to list workspaces for admin {current_admin.email}")
            raise HTTPException(status_code=500, detail=str(e))

    @get("/{workspace_id}", operation_id="get_workspace_details", response_model=WorkspaceDetailsResponse)
    async def get_workspace_details(
        self,
        workspace_id: UUID,
        current_admin: CurrentAdminUser,
        service: AdminWorkspaceServiceDep,
    ) -> WorkspaceDetailsResponse:
        """
        Get detailed information about a specific workspace (Admin only).

        This endpoint provides complete workspace details including:
        - Workspace information and status
        - Owner details
        - Current subscription information
        - Member list with roles
        - Activity logs

        Args:
            workspace_id: ID of the workspace to retrieve
            current_admin: Current authenticated admin user
            service: Admin workspace service

        Returns:
            WorkspaceDetailsResponse: Complete workspace details

        Raises:
            HTTPException:
                - 404: If workspace not found
                - 403: If user is not admin
                - 500: If database operations fail
        """
        try:
            logger.info(f"Admin {current_admin.email} getting details for workspace {workspace_id}")
            return service.get_workspace_details(workspace_id)

        except Exception as e:
            logger.exception(f"Failed to get workspace details for {workspace_id}")
            raise HTTPException(status_code=500, detail=str(e))

    @put(
        "/{workspace_id}/subscription",
        operation_id="update_workspace_subscription",
        response_model=AdminWorkspaceActionResponse,
    )
    async def update_workspace_subscription(
        self,
        workspace_id: UUID,
        request: AdminSubscriptionUpdateRequest,
        current_admin: CurrentAdminUser,
        service: AdminWorkspaceServiceDep,
    ) -> AdminWorkspaceActionResponse:
        """
        Update workspace subscription (Admin only).

        Unified endpoint for all subscription updates:
        - **renew**: Extend subscription by specified months
        - **upgrade_plan**: Change to a different plan
        - **trial_to_paid**: Convert trial to paid subscription
        - **change_billing**: Switch between monthly/yearly billing

        Args:
            workspace_id: ID of the workspace
            request: Update request with action and parameters
            current_admin: Current authenticated admin user
            service: Admin workspace service

        Returns:
            AdminWorkspaceActionResponse: Update confirmation with details

        Example Requests:
        ```json
        // Renew for 3 months
        {"action": "renew", "extend_months": 3, "remarks": "Client renewal"}

        // Upgrade plan with billing cycle change
        {
          "action": "upgrade_plan",
          "plan_id": "premium-plan-uuid",
          "billing_cycle": "yearly",
          "remarks": "Upgrade to premium yearly"
        }

        // Convert trial to paid
        {"action": "trial_to_paid", "billing_cycle": "monthly", "remarks": "Trial conversion"}

        // Change billing cycle only
        {"action": "change_billing", "billing_cycle": "yearly", "remarks": "Switch to yearly"}
        ```
        """
        try:
            logger.info(
                f"Admin {current_admin.email} updating subscription for workspace {workspace_id}: "
                f"action={request.action}"
            )

            return service.update_workspace_subscription(workspace_id, request, current_admin)

        except Exception as e:
            logger.exception(f"Failed to renew subscription for workspace {workspace_id}")
            raise HTTPException(status_code=500, detail=str(e))

    @delete(
        "/{workspace_id}/subscription",
        operation_id="cancel_workspace_subscription",
        response_model=AdminWorkspaceActionResponse,
    )
    async def cancel_workspace_subscription(
        self,
        workspace_id: UUID,
        request: AdminSubscriptionCancelRequest,
        current_admin: CurrentAdminUser,
        service: AdminWorkspaceServiceDep,
    ) -> AdminWorkspaceActionResponse:
        """
        Cancel workspace subscription (Admin only).

        This endpoint allows admins to cancel subscriptions with optional refunds.
        The subscription is marked as cancelled but workspace data is preserved.

        Args:
            workspace_id: ID of the workspace
            request: Cancellation request with reason and refund details
            current_admin: Current authenticated admin user
            service: Admin workspace service

        Returns:
            AdminWorkspaceActionResponse: Cancellation confirmation with details

        Example Request:
        ```json
        {
            "reason": "Customer requested cancellation",
            "refund_amount": 0.00,
            "effective_date": "2024-01-15"
        }
        ```
        """
        try:
            logger.info(f"Admin {current_admin.email} cancelling subscription for workspace {workspace_id}")

            return service.cancel_workspace_subscription(workspace_id, request, current_admin)

        except Exception as e:
            logger.exception(f"Failed to cancel subscription for workspace {workspace_id}")
            raise HTTPException(status_code=500, detail=str(e))

    @patch(
        "/{workspace_id}/status", operation_id="update_workspace_status", response_model=AdminWorkspaceActionResponse
    )
    async def update_workspace_status(
        self,
        workspace_id: UUID,
        request: WorkspaceStatusUpdateRequest,
        current_admin: CurrentAdminUser,
        service: AdminWorkspaceServiceDep,
    ) -> AdminWorkspaceActionResponse:
        """
        Update workspace status (Admin only).

        This endpoint allows admins to enable or archive workspaces while preserving all data.
        Archived workspaces are not accessible to users but data is retained.

        Args:
            workspace_id: ID of the workspace
            request: Status update request with new status and reason
            current_admin: Current authenticated admin user
            service: Admin workspace service

        Returns:
            AdminWorkspaceActionResponse: Status update confirmation

        Example Request:
        ```json
        {
            "status": "archived",
            "reason": "Customer account suspended"
        }
        ```
        """
        try:
            logger.info(f"Admin {current_admin.email} updating status for workspace {workspace_id} to {request.status}")

            return service.update_workspace_status(workspace_id, request, current_admin)

        except Exception as e:
            logger.exception(f"Failed to update workspace status for {workspace_id}")
            raise HTTPException(status_code=500, detail=str(e))

    @get("/expiring", operation_id="get_expiring_workspaces", response_model=AdminWorkspaceListResponse)
    async def get_expiring_workspaces(
        self,
        current_admin: CurrentAdminUser,
        service: AdminWorkspaceServiceDep,
        pagination: LargeDatasetPaginationDep,
        days: int = Query(7, description="Number of days to look ahead", ge=1, le=30),
        query: str | None = Query(None, description="Search by workspace name or owner email"),
        sort_by: str = Query("subscription_end_date", description="Sort field"),
        sort_order: str = Query("asc", description="Sort order (asc/desc)"),
    ) -> AdminWorkspaceListResponse:
        """
        Get workspaces expiring within specified days (Admin only).

        This endpoint helps admins identify workspaces that need attention
        due to upcoming subscription expiry.

        Args:
            current_admin: Current authenticated admin user
            service: Admin workspace service
            pagination: Pagination parameters (page, page_size)
            days: Number of days to look ahead (1-30)
            query: Optional search query for workspace name or owner email
            sort_by: Field to sort by
            sort_order: Sort order (asc/desc)

        Returns:
            AdminWorkspaceListResponse: Paginated list of expiring workspaces

        Raises:
            HTTPException:
                - 403: If user is not admin
                - 500: If database operations fail
        """
        try:
            logger.info(f"Admin {current_admin.email} getting workspaces expiring in {days} days")

            # Extract pagination parameters
            page, page_size = pagination

            # Build search request
            filters = WorkspaceFilters(page=page, page_size=page_size, sort_by=sort_by, sort_order=sort_order)
            search_request = WorkspaceSearchRequest(query=query, filters=filters)

            return service.get_expiring_workspaces(days, search_request)

        except Exception as e:
            logger.exception("Failed to get expiring workspaces")
            raise HTTPException(status_code=500, detail=str(e))

    @get("/trials", operation_id="get_trial_workspaces", response_model=AdminWorkspaceListResponse)
    async def get_trial_workspaces(
        self,
        current_admin: CurrentAdminUser,
        service: AdminWorkspaceServiceDep,
        pagination: LargeDatasetPaginationDep,
        query: str | None = Query(None, description="Search by workspace name or owner email"),
        sort_by: str = Query("created_at", description="Sort field"),
        sort_order: str = Query("desc", description="Sort order (asc/desc)"),
    ) -> AdminWorkspaceListResponse:
        """
        Get all trial workspaces (Admin only).

        This endpoint helps admins identify and manage trial subscriptions.

        Args:
            current_admin: Current authenticated admin user
            service: Admin workspace service
            pagination: Pagination parameters (page, page_size)
            query: Optional search query for workspace name or owner email
            sort_by: Field to sort by
            sort_order: Sort order (asc/desc)

        Returns:
            AdminWorkspaceListResponse: Paginated list of trial workspaces

        Raises:
            HTTPException:
                - 403: If user is not admin
                - 500: If database operations fail
        """
        try:
            logger.info(f"Admin {current_admin.email} getting trial workspaces")

            # Extract pagination parameters
            page, page_size = pagination

            # Build search request
            filters = WorkspaceFilters(page=page, page_size=page_size, sort_by=sort_by, sort_order=sort_order)
            search_request = WorkspaceSearchRequest(query=query, filters=filters)

            return service.get_trial_workspaces(search_request)

        except Exception as e:
            logger.exception("Failed to get trial workspaces")
            raise HTTPException(status_code=500, detail=str(e))
