"""Plan routes for subscription plan management."""

import logging
from uuid import UUID

from fastapi import HTTPException, Query

from ...dependencies.auth import Current<PERSON><PERSON><PERSON><PERSON><PERSON>, CurrentUser
from ...dependencies.pagination import StandardPaginationDep
from ...dependencies.plan import PlanServiceDep
from ...exceptions import AccountError, DatabaseError
from ...models.plan import (
    PlanCreateRequest,
    PlanCreateResponse,
    PlanDeleteResponse,
    PlanListResponse,
    PlanResponse,
    PlanUpdateRequest,
    PlanUpdateResponse,
)
from ..base_router import BaseRouter
from ..cbv import cbv, delete, get, post, put

logger = logging.getLogger(__name__)

plans_router = BaseRouter(
    prefix="/v1/plans",
    tags=["plans"],
)


@cbv(plans_router)
class PlansController:
    """Plans controller for subscription plan management."""

    @post("/", operation_id="create_plan", response_model=PlanCreateResponse, status_code=201)
    async def create_plan(
        self,
        request: PlanCreateRequest,
        plan_service: PlanServiceDep,
        current_user: CurrentAdminUser,  # Only admins can create plans
    ) -> PlanCreateResponse:
        """
        Create a new subscription plan.

        This endpoint allows administrators to create new subscription plans
        with comprehensive configuration options including pricing, trials,
        and feature definitions.

        Args:
            request: Plan creation request data
            plan_service: Plan service dependency
            current_user: Current authenticated admin user

        Returns:
            PlanCreateResponse: Created plan data

        Raises:
            HTTPException: If plan creation fails
        """
        try:
            logger.info(f"Creating plan: {request.name} by admin {current_user.email}")

            # Create plan with admin user as creator
            plan = plan_service.create_plan(request, created_by=current_user.id)

            # Convert to response model
            plan_response = PlanResponse.model_validate(plan)

            logger.info(f"Successfully created plan: {plan.name} (ID: {plan.id})")

            return PlanCreateResponse(result="success", data=plan_response, message="Plan created successfully")

        except AccountError as e:
            logger.warning(f"Plan creation failed for {request.name}: {e.message}")
            raise HTTPException(status_code=400, detail=e.message) from e

        except DatabaseError as e:
            logger.exception(f"Database error creating plan {request.name}: {e.message}")
            raise HTTPException(status_code=500, detail="Failed to create plan") from e

        except Exception as e:
            logger.exception(f"Unexpected error creating plan {request.name}")
            raise HTTPException(status_code=500, detail="Plan creation failed") from e

    @get("/", operation_id="list_plans", response_model=PlanListResponse)
    async def list_plans(
        self,
        plan_service: PlanServiceDep,
        current_user: CurrentUser,  # Authenticated users can list plans
        pagination: StandardPaginationDep,
        active_only: bool = Query(None, description="Filter to active plans only (admin only override)"),
        public_only: bool = Query(None, description="Filter to public plans only (admin only override)"),
    ) -> PlanListResponse:
        """
        List subscription plans with pagination and filtering.

        Simple single endpoint that works for both regular users and admins:
        - Regular users: Automatically see only active + public plans (UI-friendly)
        - Admin users: Can optionally override filters for management

        Args:
            plan_service: Plan service dependency
            current_user: Current authenticated user
            pagination: Pagination parameters (page, page_size)
            active_only: Filter override (admin only)
            public_only: Filter override (admin only)

        Returns:
            PlanListResponse: Paginated list of plans

        Raises:
            HTTPException: If listing fails
        """
        try:
            page, page_size = pagination
            logger.info(f"Listing plans for user {current_user.email} (page: {page}, size: {page_size})")

            # Set defaults and restrictions based on user type
            if not current_user.is_admin:
                # Regular users are FORCED to see only active + public plans
                # They cannot override these filters for security
                active_only = True
                public_only = True
                logger.info("Non-admin user: forcing active_only=True, public_only=True")
            else:
                # Admins can see all plans by default (for management purposes)
                if active_only is None:
                    active_only = False
                if public_only is None:
                    public_only = False

            logger.info(
                f"Filters applied - active_only: {active_only}, public_only: {public_only}, "
                f"is_admin: {current_user.is_admin}"
            )

            # Get plans with pagination
            plans, total = plan_service.list_plans(
                page=page, page_size=page_size, active_only=active_only, public_only=public_only
            )

            # Convert to response models
            plan_responses = [PlanResponse.model_validate(plan) for plan in plans]

            # Calculate pagination info
            total_pages = (total + page_size - 1) // page_size

            return PlanListResponse(
                plans=plan_responses, total=total, page=page, page_size=page_size, total_pages=total_pages
            )

        except DatabaseError as e:
            logger.exception(f"Database error listing plans: {e.message}")
            raise HTTPException(status_code=500, detail="Failed to list plans") from e

        except Exception as e:
            logger.exception("Unexpected error listing plans")
            raise HTTPException(status_code=500, detail="Failed to list plans") from e

    @get("/{plan_id}", operation_id="get_plan", response_model=PlanResponse)
    async def get_plan(
        self,
        plan_id: UUID,
        plan_service: PlanServiceDep,
        current_user: CurrentUser,  # Authenticated users can view plans
    ) -> PlanResponse:
        """
        Get a specific plan by ID.

        This endpoint allows authenticated users to retrieve detailed information
        about a specific subscription plan. Regular users can only see active and
        public plans, while admins can see all plans.

        Args:
            plan_id: Plan ID
            plan_service: Plan service dependency
            current_user: Current authenticated user

        Returns:
            PlanResponse: Plan data

        Raises:
            HTTPException: If plan not found or access fails
        """
        try:
            logger.info(f"Getting plan {plan_id} for user {current_user.email}")

            # Apply filtering based on user type
            if not current_user.is_admin:
                # Regular users can only see active + public plans
                plan = plan_service.get_plan_by_id_filtered(plan_id, active_only=True, public_only=True)
                logger.info("Non-admin user: applying active_only=True, public_only=True filters")
            else:
                # Admins can see all plans
                plan = plan_service.get_plan_by_id(plan_id)
                logger.info("Admin user: no filters applied")

            if not plan:
                raise HTTPException(status_code=404, detail="Plan not found")

            # Convert to response model
            return PlanResponse.model_validate(plan)

        except HTTPException:
            # Re-raise HTTP exceptions
            raise

        except DatabaseError as e:
            logger.exception(f"Database error getting plan {plan_id}: {e.message}")
            raise HTTPException(status_code=500, detail="Failed to retrieve plan") from e

        except Exception as e:
            logger.exception(f"Unexpected error getting plan {plan_id}")
            raise HTTPException(status_code=500, detail="Failed to retrieve plan") from e

    @put("/{plan_id}", operation_id="update_plan", response_model=PlanUpdateResponse)
    async def update_plan(
        self,
        plan_id: UUID,
        request: PlanUpdateRequest,
        plan_service: PlanServiceDep,
        current_user: CurrentAdminUser,  # Only admins can update plans
    ) -> PlanUpdateResponse:
        """
        Update an existing subscription plan.

        This endpoint allows administrators to update existing subscription plans
        with new configuration options, pricing, or feature definitions.

        Args:
            plan_id: Plan ID to update
            request: Plan update request data
            plan_service: Plan service dependency
            current_user: Current authenticated admin user

        Returns:
            PlanUpdateResponse: Updated plan data

        Raises:
            HTTPException: If plan update fails
        """
        try:
            logger.info(f"Updating plan {plan_id} by admin {current_user.email}")

            # Update plan
            plan = plan_service.update_plan(plan_id, request)

            # Convert to response model
            plan_response = PlanResponse.model_validate(plan)

            logger.info(f"Successfully updated plan: {plan.name} (ID: {plan_id})")

            return PlanUpdateResponse(result="success", data=plan_response, message="Plan updated successfully")

        except AccountError as e:
            logger.warning(f"Plan update failed for {plan_id}: {e.message}")
            raise HTTPException(status_code=e.status_code, detail=e.message) from e

        except DatabaseError as e:
            logger.exception(f"Database error updating plan {plan_id}: {e.message}")
            raise HTTPException(status_code=500, detail="Failed to update plan") from e

        except Exception as e:
            logger.exception(f"Unexpected error updating plan {plan_id}")
            raise HTTPException(status_code=500, detail="Plan update failed") from e

    @delete("/{plan_id}", operation_id="delete_plan", response_model=PlanDeleteResponse)
    async def delete_plan(
        self,
        plan_id: UUID,
        plan_service: PlanServiceDep,
        current_user: CurrentAdminUser,  # Only admins can delete plans
    ) -> PlanDeleteResponse:
        """
        Delete a subscription plan.

        This endpoint allows administrators to delete existing subscription plans.
        Use with caution as this operation cannot be undone.

        Args:
            plan_id: Plan ID to delete
            plan_service: Plan service dependency
            current_user: Current authenticated admin user

        Returns:
            PlanDeleteResponse: Deletion confirmation

        Raises:
            HTTPException: If plan deletion fails
        """
        try:
            logger.info(f"Deleting plan {plan_id} by admin {current_user.email}")

            # Delete plan
            plan_service.delete_plan(plan_id)

            logger.info(f"Successfully deleted plan: {plan_id}")

            return PlanDeleteResponse(
                result="success",
                message="Plan deleted successfully",
                deleted_plan_id=str(plan_id),
            )

        except AccountError as e:
            logger.warning(f"Plan deletion failed for {plan_id}: {e.message}")
            raise HTTPException(status_code=e.status_code, detail=e.message) from e

        except DatabaseError as e:
            logger.exception(f"Database error deleting plan {plan_id}: {e.message}")
            raise HTTPException(status_code=500, detail="Failed to delete plan") from e

        except Exception as e:
            logger.exception(f"Unexpected error deleting plan {plan_id}")
            raise HTTPException(status_code=500, detail="Plan deletion failed") from e

    @get("/code/{plan_code}", operation_id="get_plan_by_code", response_model=PlanResponse)
    async def get_plan_by_code(
        self,
        plan_code: str,
        plan_service: PlanServiceDep,
        current_user: CurrentUser,  # Authenticated users can view plans
    ) -> PlanResponse:
        """
        Get a specific plan by code.

        This endpoint allows authenticated users to retrieve detailed information
        about a specific subscription plan using its code. Regular users can only
        see active and public plans, while admins can see all plans.

        Args:
            plan_code: Plan code
            plan_service: Plan service dependency
            current_user: Current authenticated user

        Returns:
            PlanResponse: Plan data

        Raises:
            HTTPException: If plan not found or access fails
        """
        try:
            logger.info(f"Getting plan by code {plan_code} for user {current_user.email}")

            # Apply filtering based on user type
            if not current_user.is_admin:
                # Regular users can only see active + public plans
                plan = plan_service.get_plan_by_code_filtered(plan_code, active_only=True, public_only=True)
                logger.info("Non-admin user: applying active_only=True, public_only=True filters")
            else:
                # Admins can see all plans
                plan = plan_service.get_plan_by_code(plan_code)
                logger.info("Admin user: no filters applied")

            if not plan:
                raise HTTPException(status_code=404, detail="Plan not found")

            # Convert to response model
            return PlanResponse.model_validate(plan)

        except HTTPException:
            # Re-raise HTTP exceptions
            raise

        except DatabaseError as e:
            logger.exception(f"Database error getting plan by code {plan_code}: {e.message}")
            raise HTTPException(status_code=500, detail="Failed to retrieve plan") from e

        except Exception as e:
            logger.exception(f"Unexpected error getting plan by code {plan_code}")
            raise HTTPException(status_code=500, detail="Failed to retrieve plan") from e
