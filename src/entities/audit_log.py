from uuid import UUID, uuid4

from sqlalchemy import Index, PrimaryKeyConstraint
from sqlmodel import Field

from .base import Base


class AuditLog(Base, table=True):
    """AuditLog entity for tracking system actions and changes."""

    __tablename__ = "audit_logs"
    __table_args__ = (
        PrimaryKeyConstraint("id", name="audit_log_pkey"),
        Index("audit_log_actor_idx", "actor_id"),
        Index("audit_log_action_idx", "action"),
        Index("audit_log_target_idx", "target_id", "target_type"),
        Index("audit_log_success_idx", "success"),
        Index("audit_log_created_at_idx", "created_at"),
    )

    id: UUID = Field(default_factory=uuid4)

    actor_id: UUID | None = Field(default=None, foreign_key="accounts.id", nullable=True)
    action: str = Field(nullable=False, max_length=255)
    target_id: UUID = Field(nullable=False)
    target_type: str = Field(nullable=False, max_length=64)
    success: bool = Field(default=True, nullable=False)
    remarks: str | None = Field(default=None)

    @property
    def is_successful(self) -> bool:
        """Check if the action was successful."""
        return self.success

    @property
    def is_failed(self) -> bool:
        """Check if the action failed."""
        return not self.success

    def mark_as_failed(self, error_message: str | None = None) -> None:
        """Mark the audit log as failed."""
        self.success = False
        if error_message:
            self.remarks = error_message

    def mark_as_successful(self, success_message: str | None = None) -> None:
        """Mark the audit log as successful."""
        self.success = True
        if success_message:
            self.remarks = success_message

    def add_remarks(self, remarks: str) -> None:
        """Add remarks to the audit log."""
        self.remarks = remarks
