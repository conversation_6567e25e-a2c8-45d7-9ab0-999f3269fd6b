from uuid import UUID, uuid4

from sqlalchemy import Index, PrimaryKeyConstraint
from sqlmodel import Field

from .base import Base
from .status import WorkspaceStatus


class Workspace(Base, table=True):
    """Workspace entity for organizing user workspaces."""

    __tablename__ = "workspaces"
    __table_args__ = (
        PrimaryKeyConstraint("id", name="workspace_pkey"),
        Index("workspace_name_idx", "name"),
        Index("workspace_status_idx", "status"),
        Index("workspace_owner_idx", "owner_id"),
        Index("workspace_deleted_idx", "is_deleted"),
    )

    id: UUID = Field(default_factory=uuid4)

    name: str = Field(nullable=False, max_length=255)
    status: WorkspaceStatus = Field(default=WorkspaceStatus.ACTIVE, nullable=False)
    owner_id: UUID | None = Field(default=None, foreign_key="accounts.id", nullable=True)
    is_deleted: bool = Field(default=False, nullable=False)

    @property
    def is_active(self) -> bool:
        """Check if workspace is active and not deleted."""
        return self.status == WorkspaceStatus.ACTIVE and not self.is_deleted

    def archive(self) -> None:
        """Archive the workspace."""
        self.status = WorkspaceStatus.ARCHIVED

    def activate(self) -> None:
        """Activate the workspace."""
        self.status = WorkspaceStatus.ACTIVE

    def soft_delete(self) -> None:
        """Soft delete the workspace."""
        self.is_deleted = True
        self.status = WorkspaceStatus.ARCHIVED
