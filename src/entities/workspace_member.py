from uuid import UUID, uuid4

from sqlalchemy import Index, PrimaryKeyConstraint, UniqueConstraint
from sqlmodel import Field

from .base import Base
from .status import WorkspaceRole


class WorkspaceMember(Base, table=True):
    """WorkspaceMember entity for managing workspace membership and roles."""

    __tablename__ = "workspace_members"
    __table_args__ = (
        PrimaryKeyConstraint("id", name="workspace_member_pkey"),
        UniqueConstraint("workspace_id", "account_id", name="uq_workspace_account"),
        Index("workspace_member_workspace_idx", "workspace_id"),
        Index("workspace_member_account_idx", "account_id"),
        Index("workspace_member_role_idx", "role"),
        Index("workspace_member_invited_by_idx", "invited_by"),
    )

    id: UUID = Field(default_factory=uuid4)

    workspace_id: UUID = Field(foreign_key="workspaces.id", nullable=False)
    account_id: UUID = Field(foreign_key="accounts.id", nullable=False)
    role: WorkspaceRole = Field(default=WorkspaceRole.USER, nullable=False)
    invited_by: UUID | None = Field(default=None, foreign_key="accounts.id", nullable=True)

    @property
    def is_owner(self) -> bool:
        """Check if member is workspace owner."""
        return self.role == WorkspaceRole.OWNER

    @property
    def is_admin(self) -> bool:
        """Check if member is workspace admin or owner."""
        return self.role in (WorkspaceRole.OWNER, WorkspaceRole.ADMIN)

    @property
    def can_manage_members(self) -> bool:
        """Check if member can manage other members."""
        return self.is_admin

    def promote_to_admin(self) -> None:
        """Promote member to admin role."""
        if self.role == WorkspaceRole.USER:
            self.role = WorkspaceRole.ADMIN

    def demote_to_user(self) -> None:
        """Demote member to user role."""
        if self.role == WorkspaceRole.ADMIN:
            self.role = WorkspaceRole.USER

    def transfer_ownership(self) -> None:
        """Transfer ownership to this member."""
        self.role = WorkspaceRole.OWNER
