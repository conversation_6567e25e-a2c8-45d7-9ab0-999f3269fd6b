from datetime import date
from uuid import UUID, uuid4

from sqlalchemy import Index, PrimaryKeyConstraint
from sqlmodel import Field

from .base import Base
from .status import BillingCycle, SubscriptionStatus


class Subscription(Base, table=True):
    """Subscription entity for managing workspace subscriptions to plans."""

    __tablename__ = "subscriptions"
    __table_args__ = (
        PrimaryKeyConstraint("id", name="subscription_pkey"),
        Index("subscription_workspace_idx", "workspace_id"),
        Index("subscription_plan_idx", "plan_id"),
        Index("subscription_status_idx", "status"),
        Index("subscription_current_idx", "is_current"),
        Index("subscription_trial_idx", "is_trial"),
        Index("subscription_dates_idx", "start_date", "end_date"),
        Index("subscription_previous_idx", "previous_subscription_id"),
    )

    id: UUID = Field(default_factory=uuid4)

    workspace_id: UUID = Field(foreign_key="workspaces.id", nullable=False)
    plan_id: UUID = Field(foreign_key="plans.id", nullable=False)
    billing_cycle: BillingCycle = Field(default=BillingCycle.MONTHLY, nullable=False)
    start_date: date | None = Field(default=None)
    end_date: date | None = Field(default=None)
    status: SubscriptionStatus = Field(default=SubscriptionStatus.ACTIVE, nullable=False)
    is_trial: bool = Field(default=False, nullable=False)
    is_current: bool = Field(default=True, nullable=False)
    previous_subscription_id: UUID | None = Field(default=None, foreign_key="subscriptions.id")

    @property
    def is_active(self) -> bool:
        """Check if subscription is active."""
        return self.status == SubscriptionStatus.ACTIVE

    @property
    def is_expired(self) -> bool:
        """Check if subscription is expired."""
        return self.status == SubscriptionStatus.EXPIRED

    @property
    def is_cancelled(self) -> bool:
        """Check if subscription is cancelled."""
        return self.status == SubscriptionStatus.CANCELLED

    def activate(self) -> None:
        """Activate the subscription."""
        self.status = SubscriptionStatus.ACTIVE

    def cancel(self) -> None:
        """Cancel the subscription."""
        self.status = SubscriptionStatus.CANCELLED
        self.is_current = False

    def expire(self) -> None:
        """Mark subscription as expired."""
        self.status = SubscriptionStatus.EXPIRED
        self.is_current = False

    def make_current(self) -> None:
        """Make this subscription the current one."""
        self.is_current = True

    def make_non_current(self) -> None:
        """Make this subscription non-current."""
        self.is_current = False
