from datetime import date
from uuid import UUID, uuid4

from sqlalchemy import Index, PrimaryKeyConstraint, UniqueConstraint
from sqlmodel import Field

from .base import Base


class Payment(Base, table=True):
    """Payment entity for tracking subscription payments."""

    __tablename__ = "payments"
    __table_args__ = (
        PrimaryKeyConstraint("id", name="payment_pkey"),
        UniqueConstraint("reference", name="uq_payment_reference"),
        Index("payment_subscription_idx", "subscription_id"),
        Index("payment_reference_idx", "reference"),
        Index("payment_paid_at_idx", "paid_at"),
        Index("payment_amount_idx", "amount_paid"),
    )

    id: UUID = Field(default_factory=uuid4)

    subscription_id: UUID = Field(foreign_key="subscriptions.id", nullable=False)
    paid_at: date | None = Field(default=None)
    reference: str = Field(max_length=255, unique=True)
    remarks: str | None = Field(default=None)
    amount_paid: float = Field(nullable=False)

    @property
    def is_paid(self) -> bool:
        """Check if payment has been made."""
        return self.paid_at is not None

    @property
    def is_pending(self) -> bool:
        """Check if payment is pending."""
        return self.paid_at is None

    def mark_as_paid(self, payment_date: date | None = None) -> None:
        """Mark payment as paid."""
        if payment_date is None:
            payment_date = date.today()
        self.paid_at = payment_date

    def add_remarks(self, remarks: str) -> None:
        """Add remarks to the payment."""
        self.remarks = remarks
