import json
from uuid import UUID, uuid4

from sqlalchemy import <PERSON>SO<PERSON>, Column, Index, PrimaryKeyConstraint
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.types import Text, TypeDecorator
from sqlmodel import Field

from .base import Base


class JSONBOrJSON(TypeDecorator):
    """Use JSONB for PostgreSQL, JSON for other databases."""

    impl = Text
    cache_ok = True

    def load_dialect_impl(self, dialect):
        if dialect.name == "postgresql":
            return dialect.type_descriptor(JSONB())
        else:
            return dialect.type_descriptor(JSON())

    def process_bind_param(self, value, dialect):
        if value is not None and dialect.name != "postgresql":
            return json.dumps(value)
        return value

    def process_result_value(self, value, dialect):
        if value is not None and dialect.name != "postgresql" and isinstance(value, str):
            return json.loads(value)
        return value


class Plan(Base, table=True):
    """Plan model for subscription plans."""

    __tablename__ = "plans"
    __table_args__ = (
        PrimaryKeyConstraint("id", name="plan_pkey"),
        Index("plan_name_idx", "name"),
        Index("plan_code_idx", "plan_code"),
        Index("plan_active_idx", "is_active"),
        Index("plan_public_idx", "is_public"),
    )

    id: UUID = Field(default_factory=uuid4)

    name: str = Field(nullable=False, max_length=64)  # Allow duplicate names for versioning
    plan_code: str = Field(nullable=False, unique=True, max_length=64)  # Stable internal reference
    description: str | None = Field(default=None, max_length=255)

    price_per_month: float | None = Field(default=None)
    price_per_year: float | None = Field(default=None)

    is_active: bool = Field(default=True, nullable=False)
    is_public: bool = Field(default=True, nullable=False)

    trial_available: bool = Field(default=False, nullable=False)
    trial_duration_days: int | None = Field(default=None)

    # Use JSONB for PostgreSQL, JSON for SQLite - automatic detection
    features: dict = Field(sa_column=Column(JSONBOrJSON, nullable=False))
